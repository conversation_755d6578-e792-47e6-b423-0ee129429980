import {
  Button,
  Card,
  Icon,
  Loading,
  Typography,
  ResponsiveGrid,
} from '@/shared/components/common';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import PageWrapper from '@/shared/components/common/PageWrapper';
import { useNavigate } from 'react-router-dom';

// Mock data types
interface AdsAccountSummary {
  totalAccounts: number;
  activeAccounts: number;
  totalSpend: number;
  currency: string;
  lastUpdated: string;
}

interface AdsProviderCardProps {
  provider: 'facebook' | 'google';
  title: string;
  description: string;
  icon: string;
  summary: AdsAccountSummary | null;
  isLoading: boolean;
  onManage: () => void;
  onConnect: () => void;
}

const AdsProviderCard: React.FC<AdsProviderCardProps> = ({
  provider,
  title,
  description,
  icon,
  summary,
  isLoading,
  onManage,
  onConnect,
}) => {
  const { t } = useTranslation('integration');

  const getProviderColor = () => {
    switch (provider) {
      case 'facebook':
        return 'from-blue-500 to-blue-600';
      case 'google':
        return 'from-red-500 to-red-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getProviderBg = () => {
    switch (provider) {
      case 'facebook':
        return 'bg-blue-50';
      case 'google':
        return 'bg-red-50';
      default:
        return 'bg-gray-50';
    }
  };

  return (
    <Card className="p-6 h-full flex flex-col hover:shadow-lg transition-shadow duration-200">
      {/* Header */}
      <div className="flex items-center space-x-4 mb-4">
        <div className={`w-12 h-12 bg-gradient-to-r ${getProviderColor()} rounded-xl flex items-center justify-center shadow-lg`}>
          <Icon name={icon} size="md" className="text-white" />
        </div>
        <div className="flex-1">
          <Typography variant="h3" className="text-lg font-bold text-gray-900">
            {title}
          </Typography>
          <Typography className="text-sm text-gray-600">
            {description}
          </Typography>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="flex-1 mb-6">
        {isLoading ? (
          <div className="space-y-3">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ) : summary ? (
          <div className={`${getProviderBg()} rounded-lg p-4 space-y-3`}>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Tổng tài khoản:</span>
              <span className="font-semibold text-gray-900">{summary.totalAccounts}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Đang hoạt động:</span>
              <span className="font-semibold text-green-600">{summary.activeAccounts}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Tổng chi tiêu:</span>
              <span className="font-semibold text-gray-900">
                {summary.totalSpend.toLocaleString()} {summary.currency}
              </span>
            </div>
            <div className="pt-2 border-t border-gray-200">
              <span className="text-xs text-gray-500">
                Cập nhật: {new Date(summary.lastUpdated).toLocaleDateString('vi-VN')}
              </span>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <Icon name="info" size="lg" className="text-gray-400 mx-auto mb-2" />
            <Typography className="text-sm text-gray-500">
              Chưa có tài khoản nào được kết nối
            </Typography>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="space-y-2">
        {summary && summary.totalAccounts > 0 ? (
          <>
            <Button
              variant="primary"
              onClick={onManage}
              className={`w-full bg-gradient-to-r ${getProviderColor()} hover:shadow-lg transition-all duration-200`}
            >
              <Icon name="settings" size="sm" className="mr-2" />
              {t('integration.ads.manageAccounts', 'Quản lý tài khoản')}
            </Button>
            <Button
              variant="outline"
              onClick={onConnect}
              className="w-full"
            >
              <Icon name="plus" size="sm" className="mr-2" />
              {t('integration.ads.addAccount', 'Thêm tài khoản')}
            </Button>
          </>
        ) : (
          <Button
            variant="primary"
            onClick={onConnect}
            className={`w-full bg-gradient-to-r ${getProviderColor()} hover:shadow-lg transition-all duration-200`}
          >
            <Icon name="plus" size="sm" className="mr-2" />
            {t('integration.ads.connectAccount', 'Kết nối tài khoản')}
          </Button>
        )}
      </div>
    </Card>
  );
};

/**
 * Trang tổng quan quản lý quảng cáo (Facebook Ads + Google Ads)
 */
const AdsManagementPage: React.FC = () => {
  const { t } = useTranslation('integration');
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [facebookSummary, setFacebookSummary] = useState<AdsAccountSummary | null>(null);
  const [googleSummary, setGoogleSummary] = useState<AdsAccountSummary | null>(null);

  // Mock data loading
  useEffect(() => {
    const loadSummaryData = async () => {
      setIsLoading(true);
      
      // Simulate API calls
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock Facebook Ads summary
      setFacebookSummary({
        totalAccounts: 3,
        activeAccounts: 2,
        totalSpend: 15420.50,
        currency: 'USD',
        lastUpdated: new Date().toISOString(),
      });

      // Mock Google Ads summary
      setGoogleSummary({
        totalAccounts: 2,
        activeAccounts: 1,
        totalSpend: 8750.25,
        currency: 'USD',
        lastUpdated: new Date().toISOString(),
      });

      setIsLoading(false);
    };

    loadSummaryData();
  }, []);

  const handleFacebookManage = () => {
    navigate('/integrations/facebook-ads/manage');
  };

  const handleFacebookConnect = () => {
    navigate('/integrations/facebook-ads');
  };

  const handleGoogleManage = () => {
    navigate('/integrations/google-ads/manage');
  };

  const handleGoogleConnect = () => {
    navigate('/integrations/google-ads');
  };

  return (
    <PageWrapper className="px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <Icon name="megaphone" size="md" className="text-white" />
            </div>
            <div>
              <Typography variant="h1" className="text-2xl sm:text-3xl font-bold text-gray-900">
                {t('integration.ads.title', 'Quản lý Quảng cáo')}
              </Typography>
              <Typography className="text-gray-600 mt-1">
                {t(
                  'integration.ads.description',
                  'Quản lý tài khoản quảng cáo Facebook và Google Ads của bạn từ một nơi duy nhất'
                )}
              </Typography>
            </div>
          </div>
        </div>

        {/* Ads Provider Cards */}
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
          className="gap-6"
        >
          {/* Facebook Ads Card */}
          <AdsProviderCard
            provider="facebook"
            title="Facebook Ads"
            description={t('integration.ads.facebook.description', 'Quản lý chiến dịch quảng cáo Facebook')}
            icon="facebook"
            summary={facebookSummary}
            isLoading={isLoading}
            onManage={handleFacebookManage}
            onConnect={handleFacebookConnect}
          />

          {/* Google Ads Card */}
          <AdsProviderCard
            provider="google"
            title="Google Ads"
            description={t('integration.ads.google.description', 'Quản lý chiến dịch quảng cáo Google')}
            icon="google"
            summary={googleSummary}
            isLoading={isLoading}
            onManage={handleGoogleManage}
            onConnect={handleGoogleConnect}
          />
        </ResponsiveGrid>

        {/* Quick Stats Summary */}
        {!isLoading && (facebookSummary || googleSummary) && (
          <div className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6">
            <Typography variant="h3" className="text-lg font-semibold text-gray-900 mb-4">
              {t('integration.ads.summary.title', 'Tổng quan')}
            </Typography>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="text-center">
                <Typography variant="h2" className="text-2xl font-bold text-blue-600">
                  {(facebookSummary?.totalAccounts || 0) + (googleSummary?.totalAccounts || 0)}
                </Typography>
                <Typography className="text-sm text-gray-600">
                  {t('integration.ads.summary.totalAccounts', 'Tổng tài khoản')}
                </Typography>
              </div>
              <div className="text-center">
                <Typography variant="h2" className="text-2xl font-bold text-green-600">
                  {(facebookSummary?.activeAccounts || 0) + (googleSummary?.activeAccounts || 0)}
                </Typography>
                <Typography className="text-sm text-gray-600">
                  {t('integration.ads.summary.activeAccounts', 'Đang hoạt động')}
                </Typography>
              </div>
              <div className="text-center">
                <Typography variant="h2" className="text-2xl font-bold text-purple-600">
                  {((facebookSummary?.totalSpend || 0) + (googleSummary?.totalSpend || 0)).toLocaleString()} USD
                </Typography>
                <Typography className="text-sm text-gray-600">
                  {t('integration.ads.summary.totalSpend', 'Tổng chi tiêu')}
                </Typography>
              </div>
            </div>
          </div>
        )}
      </div>
    </PageWrapper>
  );
};

export default AdsManagementPage;
