import React, { useState, useEffect } from 'react';
import { Card, Skeleton } from '@/shared/components/common';
import { LogIn, TrendingUp } from 'lucide-react';
import { cn } from '@/shared/utils/cn';
import {
  EmployeeApiService,
  type EmployeeChartApiData,
} from '../../services/api-integration.service';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from 'recharts';

interface EmployeeLoginsChartProps {
  className?: string;
}

const EmployeeLoginsChart: React.FC<EmployeeLoginsChartProps> = ({ className }) => {
  const [data, setData] = useState<EmployeeChartApiData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('🔄 Fetching employee logins chart data...');

        const result = await EmployeeApiService.getLoginsChart({
          type: 'CUSTOMER_PRODUCT',
        });
        console.log('✅ Employee Logins Chart API Response data:', result);

        setData(result);
      } catch (err) {
        console.error('❌ Error fetching employee logins chart:', err);
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const formatTooltipValue = (value: number) => {
    return [`${value} lượt`, 'Đăng nhập'];
  };

  const formatXAxisLabel = (tickItem: string) => {
    try {
      const date = new Date(tickItem);
      return date.toLocaleDateString('vi-VN', {
        month: 'short',
        day: 'numeric',
      });
    } catch {
      return tickItem;
    }
  };

  // Màu sắc gradient cho bars
  const getBarColor = (index: number) => {
    const colors = [
      '#3B82F6', // Blue
      '#10B981', // Green
      '#F59E0B', // Yellow
      '#EF4444', // Red
      '#8B5CF6', // Purple
      '#06B6D4', // Cyan
      '#84CC16', // Lime
    ];
    return colors[index % colors.length];
  };

  if (loading) {
    return (
      <Card
        className={cn('h-full', className)}
        title={
          <div className="flex items-center gap-2">
            <LogIn className="h-5 w-5" />
            Lượt đăng nhập
          </div>
        }
      >
        <div className="space-y-4">
          <Skeleton height="200px" width="100%" />
          <div className="flex justify-between">
            <Skeleton height="16px" width="80px" />
            <Skeleton height="16px" width="80px" />
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card
        className={cn('h-full', className)}
        title={
          <div className="flex items-center gap-2">
            <LogIn className="h-5 w-5" />
            Lượt đăng nhập
          </div>
        }
      >
        <div className="flex items-center justify-center h-32 text-red-500">
          <p>Lỗi: {error}</p>
        </div>
      </Card>
    );
  }

  if (!data || !data.data || data.data.length === 0) {
    return (
      <Card
        className={cn('h-full', className)}
        title={
          <div className="flex items-center gap-2">
            <LogIn className="h-5 w-5" />
            Lượt đăng nhập
          </div>
        }
      >
        <div className="flex items-center justify-center h-32 text-gray-500">
          <p>Không có dữ liệu biểu đồ</p>
        </div>
      </Card>
    );
  }

  // Tính toán thống kê
  const totalLogins = data.data.reduce((sum, item) => sum + item.value, 0);
  const averageLogins = Math.round(totalLogins / data.data.length);
  const maxLogins = Math.max(...data.data.map(item => item.value));

  return (
    <Card
      className={cn('h-full', className)}
      title={
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <LogIn className="h-5 w-5" />
            Lượt đăng nhập
          </div>
          <div className="flex items-center gap-4 text-sm">
            <div className="text-center">
              <div className="text-xs text-gray-500">Tổng</div>
              <div className="font-semibold text-blue-600">{totalLogins}</div>
            </div>
            <div className="text-center">
              <div className="text-xs text-gray-500">TB</div>
              <div className="font-semibold text-green-600">{averageLogins}</div>
            </div>
          </div>
        </div>
      }
    >
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data.data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis dataKey="date" tickFormatter={formatXAxisLabel} stroke="#6B7280" fontSize={12} />
            <YAxis stroke="#6B7280" fontSize={12} />
            <Tooltip
              formatter={formatTooltipValue}
              labelStyle={{ color: '#374151' }}
              contentStyle={{
                backgroundColor: '#F9FAFB',
                border: '1px solid #E5E7EB',
                borderRadius: '6px',
              }}
            />
            <Bar dataKey="value" radius={[4, 4, 0, 0]}>
              {data.data.map((_, index) => (
                <Cell key={`cell-${index}`} fill={getBarColor(index)} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>

      <div className="mt-4 flex justify-between items-center text-xs text-gray-500">
        <span>Biểu đồ lượt đăng nhập theo thời gian</span>
        <div className="flex items-center gap-1">
          <TrendingUp className="h-3 w-3" />
          <span>Cao nhất: {maxLogins}</span>
        </div>
      </div>
    </Card>
  );
};

export default EmployeeLoginsChart;
