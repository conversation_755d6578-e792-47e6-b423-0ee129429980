import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  Typography,
  Chip,
  Skeleton,
  ConfirmDeleteModal,
  ActionMenu,
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { FacebookAdsService } from '@/modules/integration/facebook-ads/services/facebook-ads.service';
import {
  FacebookAdsIntegration,
  FacebookAdsQueryParams,
} from '@/modules/integration/facebook-ads/types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { formatDate } from '@/shared/utils/format';
import { useNavigate } from 'react-router-dom';

/**
 * Trang quản lý Facebook Ads Integrations
 */
const FacebookAccountsPage: React.FC = () => {
  const { t } = useTranslation(['integration', 'common']);
  const { success, error: showError } = useSmartNotification();
  const navigate = useNavigate();

  // Data states
  const [facebookAdsIntegrations, setFacebookAdsIntegrations] = useState<FacebookAdsIntegration[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [totalItems, setTotalItems] = useState(0);

  // Delete modal states
  const [integrationToDelete, setIntegrationToDelete] = useState<FacebookAdsIntegration | null>(
    null
  );
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [isReloading, setIsReloading] = useState(false);

  // Column visibility
  const [, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Ref để tránh multiple concurrent calls
  const isLoadingRef = useRef(false);

  // Load Facebook Ads integrations
  const loadFacebookAdsIntegrations = useCallback(async () => {
    if (isLoadingRef.current) return;

    try {
      isLoadingRef.current = true;
      setIsLoading(true);

      const params: FacebookAdsQueryParams = {
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined,
        sortBy: 'createdAt',
        sortDirection: 'DESC',
      };

      const response = await FacebookAdsService.getIntegrations(params);
      setFacebookAdsIntegrations(response.items || []);
      setTotalItems(response.meta?.totalItems || 0);
    } catch (error) {
      console.error('Failed to load Facebook Ads integrations:', error);
      setFacebookAdsIntegrations([]);
      setTotalItems(0);

      showError({
        message: t(
          'integration:facebookAds.errors.loadFailed',
          'Không thể tải danh sách Facebook Ads integrations'
        ),
      });
    } finally {
      isLoadingRef.current = false;
      setIsLoading(false);
    }
  }, [currentPage, pageSize, searchTerm, t, showError]);

  const handleNavigateToCreate = () => {
    navigate('/integrations/facebook-ads');
  };

  // Delete integration
  const handleDelete = async () => {
    if (!integrationToDelete) return;

    try {
      setIsSubmitting(true);
      await FacebookAdsService.deleteIntegration(integrationToDelete.id);

      success({
        message: t(
          'integration:facebookAds.messages.deleteSuccess',
          'Xóa Facebook Ads integration thành công'
        ),
      });

      await loadFacebookAdsIntegrations();
      setShowDeleteConfirm(false);
      setIntegrationToDelete(null);
    } catch (error) {
      console.error('Failed to delete Facebook Ads integration:', error);
      showError({
        message: t(
          'integration:facebookAds.errors.deleteFailed',
          'Không thể xóa Facebook Ads integration'
        ),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show delete confirmation
  const handleShowDeleteConfirm = useCallback((integration: FacebookAdsIntegration) => {
    setIntegrationToDelete(integration);
    setShowDeleteConfirm(true);
  }, []);

  // View in Facebook Ads Manager
  const handleViewInFacebookAds = useCallback((accountId: string) => {
    window.open(
      `https://business.facebook.com/adsmanager/manage/accounts?act=${accountId}`,
      '_blank'
    );
  }, []);

  // Table columns
  const columns = useMemo<TableColumn<FacebookAdsIntegration>[]>(
    () => [
      {
        key: 'integrationName',
        title: t('integration:facebookAds.table.columns.name', 'Tên tích hợp'),
        dataIndex: 'integrationName',
        width: '25%',
        sortable: true,
      },
      {
        key: 'accountInfo',
        title: t('integration:facebookAds.table.columns.account', 'Tài khoản'),
        dataIndex: 'accountName',
        width: '25%',
        render: (_, record: FacebookAdsIntegration) => (
          <div>
            <Typography variant="subtitle2" className="font-medium">
              {record.accountName || 'N/A'}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground">
              ID: {record.accountId || 'N/A'}
            </Typography>
          </div>
        ),
      },
      {
        key: 'connectionStatus',
        title: t('integration:facebookAds.table.columns.status', 'Trạng thái'),
        dataIndex: 'connectionStatus',
        width: '15%',
        sortable: true,
        render: (_, record: FacebookAdsIntegration) => {
          const status = record.connectionStatus;
          if (status === 'CONNECTED') {
            return (
              <Chip variant="success" size="sm">
                <CheckCircle className="h-3 w-3 mr-1" />
                {t('integration:facebookAds.status.connected', 'Đã kết nối')}
              </Chip>
            );
          } else if (status === 'ERROR') {
            return (
              <Chip variant="danger" size="sm">
                <AlertCircle className="h-3 w-3 mr-1" />
                {t('integration:facebookAds.status.error', 'Lỗi')}
              </Chip>
            );
          } else {
            return (
              <Chip variant="warning" size="sm">
                <XCircle className="h-3 w-3 mr-1" />
                {t('integration:facebookAds.status.disconnected', 'Chưa kết nối')}
              </Chip>
            );
          }
        },
      },
      {
        key: 'createdAt',
        title: t('integration:facebookAds.table.columns.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '20%',
        sortable: true,
        render: (_, record: FacebookAdsIntegration) => (
          <Typography variant="caption" className="text-muted-foreground">
            {formatDate(new Date(record.createdAt), 'dd/MM/yyyy HH:mm')}
          </Typography>
        ),
      },
      {
        key: 'actions',
        title: t('common:actions', 'Hành động'),
        dataIndex: 'id',
        width: '15%',
        render: (_, record: FacebookAdsIntegration) => (
          <ActionMenu
            items={[
              {
                id: 'view',
                label: t('common:view', 'Xem'),
                icon: 'external-link',
                onClick: () => handleViewInFacebookAds(record.accountId),
              },
              {
                id: 'delete',
                label: t('common:delete', 'Xóa'),
                icon: 'trash',
                onClick: () => handleShowDeleteConfirm(record),
              },
            ]}
          />
        ),
      },
    ],
    [t, handleViewInFacebookAds, handleShowDeleteConfirm]
  );

  // Load data on mount
  useEffect(() => {
    loadFacebookAdsIntegrations();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Reload when pagination/search changes
  useEffect(() => {
    loadFacebookAdsIntegrations();
  }, [currentPage, pageSize, searchTerm]); // eslint-disable-line react-hooks/exhaustive-deps

  // Search handler
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
  }, []);

  // Reload handler
  const handleReload = useCallback(async () => {
    if (isReloading) return;

    setIsReloading(true);
    await loadFacebookAdsIntegrations();
    setIsReloading(false);
  }, [isReloading, loadFacebookAdsIntegrations]);

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  return (
    <div className="w-full bg-background text-foreground space-y-6 p-8">
      {/* Header */}
      <div className="mb-6">
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleNavigateToCreate}
          columns={columns.map(col => ({
            id: col.key,
            label: typeof col.title === 'string' ? col.title : col.key,
            visible: true,
          }))}
          onColumnVisibilityChange={setVisibleColumns}
          additionalIcons={[
            {
              icon: 'refresh-cw',
              tooltip: t('common:reload', 'Tải lại'),
              onClick: handleReload,
              condition: !isReloading,
            },
            {
              icon: 'loader',
              tooltip: t('common:loading', 'Đang tải...'),
              onClick: () => {},
              condition: isReloading,
            },
          ]}
        />
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('integration:facebookAds.stats.total', 'Tổng tích hợp')}
              </Typography>
              <Typography variant="h2" className="text-blue-600">
                {isLoading ? <Skeleton className="h-8 w-12" /> : totalItems}
              </Typography>
            </div>
            <CheckCircle className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('integration:facebookAds.stats.connected', 'Đã kết nối')}
              </Typography>
              <Typography variant="h2" className="text-green-600">
                {isLoading ? (
                  <Skeleton className="h-8 w-12" />
                ) : (
                  facebookAdsIntegrations.filter(
                    integration => integration.connectionStatus === 'CONNECTED'
                  ).length
                )}
              </Typography>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('integration:facebookAds.stats.disconnected', 'Chưa kết nối')}
              </Typography>
              <Typography variant="h2" className="text-orange-600">
                {isLoading ? (
                  <Skeleton className="h-8 w-12" />
                ) : (
                  facebookAdsIntegrations.filter(
                    integration => integration.connectionStatus !== 'CONNECTED'
                  ).length
                )}
              </Typography>
            </div>
            <AlertCircle className="h-8 w-8 text-orange-600" />
          </div>
        </Card>
      </div>

      {/* Table */}
      <Card>
        <Table
          columns={columns}
          data={facebookAdsIntegrations}
          loading={isLoading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalItems,
            showSizeChanger: true,
            onChange: handlePageChange,
          }}
        />
      </Card>

      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleDelete}
        title={t('integration:facebookAds.deleteConfirm.title', 'Xác nhận xóa')}
        message={
          integrationToDelete
            ? t(
                'integration:facebookAds.deleteConfirm.message',
                'Bạn có chắc chắn muốn xóa tích hợp "{{name}}" không? Hành động này không thể hoàn tác.',
                { name: integrationToDelete.integrationName }
              )
            : ''
        }
        isSubmitting={isSubmitting}
      />
    </div>
  );
};

export default FacebookAccountsPage;
