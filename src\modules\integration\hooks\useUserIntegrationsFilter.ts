/**
 * Hook để lọc integrations theo user
 * Map danh sách integrations của user vớ<PERSON> danh sách tất cả integrations có sẵn
 */

import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { getMyIntegrationTypes } from '../services/user-integration.api';
import {
  IntegrationTypeEnum,
  IntegrationType,
  UserIntegrationDto,
} from '../types/user-integration.types';
import { useQuery } from '@tanstack/react-query';

// Types cho integration items
export type IntegrationCategory =
  | 'all'
  | 'bank'
  | 'llm'
  | 'sms'
  | 'email'
  | 'social'
  | 'shipping'
  | 'calendar'
  | 'ads';

export interface IntegrationItem {
  id: string;
  title: string;
  description: string;
  icon: string;
  linkTo: string;
  category: IntegrationCategory;
  gradientColor?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  userIntegration?: UserIntegrationDto | undefined; // Thông tin tích hợp của user nếu có
  isUserIntegrated?: boolean | undefined; // Có phải user đã tích hợp không
}

// Query keys
export const USER_INTEGRATIONS_FILTER_QUERY_KEYS = {
  ALL: ['user-integrations-filter'] as const,
  LIST: () => [...USER_INTEGRATIONS_FILTER_QUERY_KEYS.ALL, 'list'] as const,
};

/**
 * Hook để lấy danh sách tất cả integrations có sẵn
 */
export const useAllIntegrations = () => {
  const { t } = useTranslation(['integration']);

  return useMemo(
    (): IntegrationItem[] => [
      // Banking integrations
      {
        id: 'mb-banking',
        title: 'MB Banking',
        description: t(
          'integration:cards.banking.mb.description',
          'Tích hợp với ngân hàng MB Bank '
        ),
        icon: 'mb-bank',
        linkTo: '/integrations/banking/mb',
        category: 'bank',
        gradientColor: 'success',
      },
      {
        id: 'acb-banking',
        title: 'ACB Banking',
        description: t('integration:cards.banking.acb.description', 'Tích hợp với ngân hàng ACB'),
        icon: 'acb-bank',
        linkTo: '/integrations/banking/acb',
        category: 'bank',
        gradientColor: 'primary',
      },
      {
        id: 'ocb-banking',
        title: 'OCB Banking',
        description: t('integration:cards.banking.ocb.description', 'Tích hợp với ngân hàng OCB'),
        icon: 'ocb-bank',
        linkTo: '/integrations/banking/ocb',
        category: 'bank',
        gradientColor: 'info',
      },
      {
        id: 'kienlong-banking',
        title: 'Kiên Long Banking',
        description: t(
          'integration:cards.banking.kienlong.description',
          'Tích hợp với ngân hàng Kiên Long Bank'
        ),
        icon: 'kienlong-bank',
        linkTo: '/integrations/banking/kienlong',
        category: 'bank',
        gradientColor: 'warning',
      },

      // AI LLM integrations
      {
        id: 'openai',
        title: 'OpenAI',
        description: t(
          'integration:cards.llm.openai.description',
          'Tích hợp với OpenAI GPT models'
        ),
        icon: 'openai',
        linkTo: '/integrations/ai/openai',
        category: 'llm',
        gradientColor: 'success',
      },
      {
        id: 'anthropic',
        title: 'Anthropic Claude',
        description: t(
          'integration:cards.llm.anthropic.description',
          'Tích hợp với Anthropic Claude AI'
        ),
        icon: 'anthropic',
        linkTo: '/integrations/ai/anthropic',
        category: 'llm',
        gradientColor: 'primary',
      },
      {
        id: 'gemini',
        title: 'Google Gemini',
        description: t(
          'integration:cards.llm.gemini.description',
          'Tích hợp với Google Gemini Pro'
        ),
        icon: 'gemini',
        linkTo: '/integrations/ai/google',
        category: 'llm',
        gradientColor: 'primary',
      },
      {
        id: 'deepseek',
        title: 'DeepSeek',
        description: t(
          'integration:cards.llm.deepseek.description',
          'Tích hợp với DeepSeek AI models'
        ),
        icon: 'deepseek',
        linkTo: '/integrations/ai/deepseek',
        category: 'llm',
        gradientColor: 'info',
      },
      {
        id: 'xai',
        title: 'XAI Grok',
        description: t('integration:cards.llm.xai.description', 'Tích hợp với XAI Grok models'),
        icon: 'grok',
        linkTo: '/integrations/ai/xai',
        category: 'llm',
        gradientColor: 'secondary',
      },

      // SMS integrations
      {
        id: 'twilio-sms',
        title: 'Twilio SMS',
        description: t(
          'integration:cards.sms.twilio.description',
          'Tích hợp với Twilio để gửi SMS'
        ),
        icon: 'mail',
        linkTo: '/integrations/sms/twilio',
        category: 'sms',
        gradientColor: 'primary',
      },

      {
        id: 'fpt-sms',
        title: 'FPT SMS',
        description: t('integration:cards.sms.fpt.description', 'Tích hợp với FPT  để gửi  SMS'),
        icon: 'fpt-logo',
        linkTo: '/integrations/sms/fpt',
        category: 'sms',
        gradientColor: 'info',
      },

      // Email integrations
      {
        id: 'email-smtp',
        title: t('integration:emailSMTP.title', 'Tạo Email SMTP'),
        description: t('integration:emailSMTP.description', 'Cấu hình máy chủ email SMTP'),
        icon: 'mail-plus',
        linkTo: '/integrations/smtp',
        category: 'email',
        gradientColor: 'primary',
      },
      {
        id: 'gmail',
        title: 'Gmail',
        description: t('integration:gmail.description', 'Kết nối với Gmail để gửi và nhận email'),
        icon: 'google-gmail',
        linkTo: '/integrations/gmail',
        category: 'email',
        gradientColor: 'primary',
      },

      // Social integrations
      {
        id: 'facebook',
        title: t('integration:facebook.title', 'Facebook'),
        description: t(
          'integration:facebook.description',
          'Quản lý các tài khoản Facebook đã liên kết'
        ),
        icon: 'facebook',
        linkTo: '/integrations/facebook',
        category: 'social',
        gradientColor: 'primary',
      },
      {
        id: 'zalo-personal',
        title: t('integration:zalo.personal.title', 'Tích hợp Zalo cá nhân'),
        description: t(
          'integration:zalo.personal.description',
          'Kết nối tài khoản Zalo cá nhân để sử dụng các tính năng marketing'
        ),
        icon: 'zaloIcon',
        linkTo: '/integrations/zalo-personal',
        category: 'social',
        gradientColor: 'info',
      },
      {
        id: 'facebook-ads',
        title: 'Facebook Ads',
        description: t(
          'integration:facebookAds.description',
          'Tích hợp với Facebook Ads để quản lý chiến dịch quảng cáo'
        ),
        icon: 'facebook',
        linkTo: '/integrations/facebook-ads',
        category: 'ads',
        gradientColor: 'primary',
      },
      {
        id: 'zalo-oa',
        title: 'Zalo OA',
        description: t(
          'integration:cards.social.zalo.description',
          'Tích hợp với Zalo Official Account'
        ),
        icon: 'zaloIcon',
        linkTo: '/integrations/social/zalo-oa',
        category: 'social',
        gradientColor: 'info',
      },

      {
        id: 'website',
        title: t('integration:website.title', 'Website'),
        description: t('integration:website.description', 'Quản lý tích hợp với website'),
        icon: 'website',
        linkTo: '/integrations/website',
        category: 'social',
        gradientColor: 'info',
      },

      // Calendar integrations
      {
        id: 'google-calendar',
        title: 'Google Calendar',
        description: t(
          'integration:calendar.description',
          'Kết nối với Google Calendar để đồng bộ lịch và sự kiện'
        ),
        icon: 'calendar',
        linkTo: '/integrations/calendar/google',
        category: 'calendar',
        gradientColor: 'primary',
      },

      // Ads integrations
      {
        id: 'google-ads',
        title: 'Google Ads',
        description: t(
          'integration:googleAds.description',
          'Kết nối với Google Ads để quản lý chiến dịch quảng cáo'
        ),
        icon: 'google-ads',
        linkTo: '/integrations/google-ads',
        category: 'ads',
        gradientColor: 'primary',
      },

      // Shipping integrations

      {
        id: 'ghtk',
        title: 'GHTK',
        description: t(
          'integration:cards.shipping.ghtk.description',
          'Tích hợp với Giao Hàng Tiết Kiệm (GHTK)'
        ),
        icon: 'ghtk-logo',
        linkTo: '/integrations/ghtk',
        category: 'shipping',
        gradientColor: 'success',
      },
      {
        id: 'ghn',
        title: 'GHN',
        description: t(
          'integration:cards.shipping.ghn.description',
          'Tích hợp với Giao Hàng Nhanh (GHN)'
        ),
        icon: 'linkedin-logo',
        linkTo: '/integrations/ghn',
        category: 'shipping',
        gradientColor: 'primary',
      },
      {
        id: 'ahamove',
        title: 'Ahamove',
        description: t(
          'integration:cards.shipping.ahamove.description',
          'Tích hợp với  Giao hàng nhanh Ahamove'
        ),
        icon: 'ahamove',
        linkTo: '/integrations/ahamove',
        category: 'shipping',
        gradientColor: 'warning',
      },
    ],
    [t]
  );
};

/**
 * Hook để map integrations của user với danh sách tất cả integrations (sử dụng API mới)
 */
export const useIntegrationsWithUserData = () => {
  const { data: userIntegrationTypes = [], isLoading: isLoadingUserIntegrations } =
    useMyIntegrationTypes();
  const allIntegrations = useAllIntegrations();

  const integrationsWithUserData = useMemo(() => {
    return allIntegrations.map(integration => {
      // Tìm user integration type tương ứng dựa trên integration id
      const integrationTypeMap: Record<string, IntegrationTypeEnum> = {
        'mb-banking': IntegrationTypeEnum.MB_BANK_SEPAY_HUB,
        'acb-banking': IntegrationTypeEnum.ACB_BANK_SEPAY_HUB,
        'ocb-banking': IntegrationTypeEnum.OCB_BANK_SEPAY_HUB,
        'kienlong-banking': IntegrationTypeEnum.KIEN_LONG_BANK_SEPAY_HUB,
        openai: IntegrationTypeEnum.OPEN_AI_KEY,
        anthropic: IntegrationTypeEnum.ANTHROPIC_CLAUDE,
        gemini: IntegrationTypeEnum.GOOGLE_GEMINI,
        deepseek: IntegrationTypeEnum.DEEP_SEEK,

        'xai-grok': IntegrationTypeEnum.XAI_GROK,
        'twilio-sms': IntegrationTypeEnum.SMS_TWILIO,

        'fpt-sms': IntegrationTypeEnum.SMS_FPT,
        'email-smtp': IntegrationTypeEnum.EMAIL_SMTP,
        gmail: IntegrationTypeEnum.GMAIL_OAUTH,
        facebook: IntegrationTypeEnum.FACEBOOK_PAGE,
        'facebook-ads': IntegrationTypeEnum.FACEBOOK_ADS,
        'zalo-oa': IntegrationTypeEnum.ZALO_OFFICIAL_ACCOUNT,
        'google-calendar': IntegrationTypeEnum.GOOGLE_CALENDAR,
        'google-ads': IntegrationTypeEnum.GOOGLE_ADS,
        analytics: IntegrationTypeEnum.ANALYTICS,
        shipping: IntegrationTypeEnum.SHIPPING,
      };

      const expectedType = integrationTypeMap[integration.id];
      const userIntegrationType = userIntegrationTypes.find(uit => uit.type === expectedType);

      return {
        ...integration,
        userIntegration: userIntegrationType
          ? ({
              id: `${userIntegrationType.userId}-${userIntegrationType.type}` as unknown as number, // ID should be number but we're creating a string
              integrationName: integration.title,
              userId: userIntegrationType.userId,
              type: expectedType as unknown as IntegrationType, // Convert to IntegrationType for compatibility
              status: 'ACTIVE' as const,
              info: {},
              ownedType: 'USER' as const, // Add required ownedType property
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            } as unknown as UserIntegrationDto)
          : undefined,
        isUserIntegrated: !!userIntegrationType,
      };
    });
  }, [allIntegrations, userIntegrationTypes]);

  return {
    integrations: integrationsWithUserData,
    isLoading: isLoadingUserIntegrations,
  };
};

/**
 * Hook để lấy chỉ những integrations mà user đã tích hợp
 */
export const useUserIntegratedOnly = () => {
  const { integrations, isLoading } = useIntegrationsWithUserData();

  const userIntegratedIntegrations = useMemo(() => {
    return integrations.filter(integration => integration.isUserIntegrated);
  }, [integrations]);

  return {
    integrations: userIntegratedIntegrations,
    isLoading,
  };
};

/**
 * Hook để lấy danh sách loại tích hợp mà user đã tích hợp từ API mới
 */
export const useMyIntegrationTypes = () => {
  return useQuery({
    queryKey: [...USER_INTEGRATIONS_FILTER_QUERY_KEYS.ALL, 'my-types'],
    queryFn: async () => {
      const response = await getMyIntegrationTypes();
      return response.result;
    },
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để mapping từ IntegrationTypeEnum sang integration items
 */
export const useIntegrationTypeMapping = () => {
  const { t } = useTranslation(['integration']);

  return useMemo(
    () =>
      (type: IntegrationTypeEnum): IntegrationItem | null => {
        const typeToItemMap: Record<IntegrationTypeEnum, IntegrationItem> = {
          [IntegrationTypeEnum.FACEBOOK_PAGE]: {
            id: 'facebook',
            title: t('integration:facebook.title', 'Facebook'),
            description: t(
              'integration:facebook.description',
              'Tích hợp với Facebook để quản lý fanpage và quảng cáo'
            ),
            icon: 'facebook',
            linkTo: '/integrations/facebook',
            category: 'social',
            gradientColor: 'primary',
          },
          [IntegrationTypeEnum.FACEBOOK_ADS]: {
            id: 'facebook-ads',
            title: 'Facebook Ads',
            description: t(
              'integration:facebookAds.description',
              'Tích hợp với Facebook Ads để quản lý chiến dịch quảng cáo'
            ),
            icon: 'facebook',
            linkTo: '/integrations/facebook-ads',
            category: 'ads',
            gradientColor: 'primary',
          },
          [IntegrationTypeEnum.MB_BANK_SEPAY_HUB]: {
            id: 'mb-banking',
            title: 'MB Banking',
            description: 'Tích hợp với ngân hàng MB Bank ',
            icon: 'mb-bank',
            linkTo: '/integrations/banking/mb',
            category: 'bank',
            gradientColor: 'success',
          },
          [IntegrationTypeEnum.OCB_BANK_SEPAY_HUB]: {
            id: 'ocb-banking',
            title: 'OCB Banking',
            description: 'Tích hợp với ngân hàng OCB ',
            icon: 'ocb-bank',
            linkTo: '/integrations/banking/ocb',
            category: 'bank',
            gradientColor: 'info',
          },
          [IntegrationTypeEnum.ACB_BANK_SEPAY_HUB]: {
            id: 'acb-banking',
            title: 'ACB Banking',
            description: 'Tích hợp với ngân hàng ACB ',
            icon: 'acb-bank',
            linkTo: '/integrations/banking/acb',
            category: 'bank',
            gradientColor: 'primary',
          },
          [IntegrationTypeEnum.KIEN_LONG_BANK_SEPAY_HUB]: {
            id: 'kienlong-banking',
            title: 'Kiên Long Banking',
            description: 'Tích hợp với ngân hàng Kiên Long Bank',
            icon: 'kienlong-bank',
            linkTo: '/integrations/banking/kienlong',
            category: 'bank',
            gradientColor: 'warning',
          },
          [IntegrationTypeEnum.EMAIL_SMTP]: {
            id: 'email-smtp',
            title: t('integration:emailSMTP.title', 'Quản lý Email SMTP'),
            description: t(
              'integration:emailSMTP.descriptionManagement',
              'Cấu hình máy chủ email SMTP để gửi email tự động'
            ),
            icon: 'mail-plus',
            linkTo: '/integrations/email-servers',
            category: 'email',
            gradientColor: 'primary',
          },
          [IntegrationTypeEnum.GMAIL_OAUTH]: {
            id: 'gmail',
            title: 'Gmail',
            description: t(
              'integration:gmail.description',
              'Kết nối với Gmail để gửi và nhận email'
            ),
            icon: 'mail',
            linkTo: '/integrations/gmail',
            category: 'email',
            gradientColor: 'success',
          },

          [IntegrationTypeEnum.SMS_TWILIO]: {
            id: 'twilio-sms',
            title: t('integration:twilio.title', 'Twilio SMS'),
            description: t('integration:twilio.description', 'Tích hợp với Twilio để gửi SMS'),
            icon: 'message-circle',
            linkTo: '/integrations/sms/twilio',
            category: 'sms',
            gradientColor: 'primary',
          },
          [IntegrationTypeEnum.SMS_FPT]: {
            id: 'fpt-sms',
            title: 'FPT SMS',
            description: t('integration:cards.sms.fpt.description', 'Tích hợp với FPT để gửi SMS'),
            icon: 'fpt-logo',
            linkTo: '/integrations/sms/fpt',
            category: 'sms',
            gradientColor: 'info',
          },
          [IntegrationTypeEnum.ANALYTICS]: {
            id: 'analytics',
            title: 'Analytics',
            description: 'Tích hợp với dịch vụ phân tích',
            icon: 'chart',
            linkTo: '/integrations/analytics',
            category: 'social',
            gradientColor: 'success',
          },
          [IntegrationTypeEnum.SHIPPING]: {
            id: 'shipping',
            title: t('integration:shipping.title', 'Quản lý Vận chuyển'),
            description: t(
              'integration:shipping.description',
              'Tích hợp với các nhà vận chuyển GHN, GHTK,...'
            ),
            icon: 'truck',
            linkTo: '/integrations/shipping',
            category: 'shipping',
            gradientColor: 'warning',
          },

          [IntegrationTypeEnum.ZALO_OFFICIAL_ACCOUNT]: {
            id: 'zalo-oa',
            title: 'Zalo OA',
            description: 'Tích hợp với Zalo Official Account',
            icon: 'zaloIcon',
            linkTo: '/integrations/social/zalo-oa',
            category: 'social',
            gradientColor: 'info',
          },
          [IntegrationTypeEnum.OPEN_AI_KEY]: {
            id: 'openai',
            title: 'OpenAI',
            description: t(
              'integration:cards.llm.openai.description',
              'Tích hợp với OpenAI GPT models'
            ),
            icon: 'openai-red',
            linkTo: '/integrations/ai/openai',
            category: 'llm',
            gradientColor: 'success',
          },

          [IntegrationTypeEnum.ANTHROPIC_CLAUDE]: {
            id: 'anthropic',
            title: 'Anthropic Claude',
            description: t(
              'integration:cards.llm.anthropic.description',
              'Tích hợp với Anthropic Claude AI'
            ),
            icon: 'openai-red',
            linkTo: '/integrations/ai/anthropic',
            category: 'llm',
            gradientColor: 'primary',
          },
          [IntegrationTypeEnum.GOOGLE_GEMINI]: {
            id: 'gemini',
            title: 'Google Gemini',
            description: t(
              'integration:cards.llm.gemini.description',
              'Tích hợp với Google Gemini Pro'
            ),
            icon: 'openai-red',
            linkTo: '/integrations/ai/gemini',
            category: 'llm',
            gradientColor: 'primary',
          },
          [IntegrationTypeEnum.DEEP_SEEK]: {
            id: 'deepseek',
            title: 'DeepSeek',
            description: t(
              'integration:cards.llm.deepseek.description',
              'Tích hợp với DeepSeek AI models'
            ),
            icon: 'openai-red',
            linkTo: '/integrations/ai/deepseek',
            category: 'llm',
            gradientColor: 'info',
          },

          [IntegrationTypeEnum.XAI_GROK]: {
            id: 'xai-grok',
            title: 'XAI Grok',
            description: t('integration:cards.llm.xai.description', 'Tích hợp với XAI Grok models'),
            icon: 'openai-red',
            linkTo: '/integrations/ai/xai',
            category: 'llm',
            gradientColor: 'secondary',
          },
          [IntegrationTypeEnum.GOOGLE_CALENDAR]: {
            id: 'google-calendar',
            title: 'Google Calendar',
            description: t(
              'integration:calendar.description',
              'Kết nối với Google Calendar để đồng bộ lịch và sự kiện'
            ),
            icon: 'calendar',
            linkTo: '/integrations/calendar/google',
            category: 'calendar',
            gradientColor: 'primary',
          },
          [IntegrationTypeEnum.GOOGLE_ADS]: {
            id: 'google-ads',
            title: 'Google Ads',
            description: t(
              'integration:googleAds.description',
              'Kết nối với Google Ads để quản lý chiến dịch quảng cáo'
            ),
            icon: 'google-ads',
            linkTo: '/integrations/google-ads',
            category: 'ads',
            gradientColor: 'primary',
          },
        };

        return typeToItemMap[type] || null;
      },
    [t]
  );
};

/**
 * Hook để lấy chỉ những integrations mà user đã tích hợp từ API mới
 */
export const useUserIntegratedOnlyFromAPI = () => {
  const { data: userIntegrationTypes = [], isLoading } = useMyIntegrationTypes();
  const mapIntegrationTypeToItem = useIntegrationTypeMapping();

  const userIntegratedIntegrations = useMemo(() => {
    return userIntegrationTypes
      .map(userType => mapIntegrationTypeToItem(userType.type))
      .filter((item): item is IntegrationItem => item !== null);
  }, [userIntegrationTypes, mapIntegrationTypeToItem]);

  return {
    integrations: userIntegratedIntegrations,
    isLoading,
  };
};

/**
 * Hook để lấy chỉ những integrations mà user đã tích hợp từ API mới
 * với linkTo được override để trỏ đến trang quản lý thay vì trang tích hợp mới
 */
export const useUserIntegratedOnlyForMyIntegrationsPage = () => {
  const { data: userIntegrationTypes = [], isLoading } = useMyIntegrationTypes();
  const mapIntegrationTypeToItem = useIntegrationTypeMapping();
  const { t } = useTranslation(['integration']);

  const userIntegratedIntegrations = useMemo(() => {
    const baseIntegrations = userIntegrationTypes
      .map(userType => {
        const item = mapIntegrationTypeToItem(userType.type);
        if (!item) return null;

        // Loại bỏ card MB Banking vì đã có card quản lý riêng
        if (item.id === 'mb-banking') return null;

        // Override linkTo để trỏ đến trang quản lý thay vì trang tích hợp mới
        const managementLinkMap: Record<string, string> = {
          'mb-banking': '/integrations/bank-accounts/mb',
          'acb-banking': '/integrations/bank-accounts/acb',
          'ocb-banking': '/integrations/bank-accounts/ocb',
          'kienlong-banking': '/integrations/bank-accounts/kienlong',
          // Các integration khác có thể giữ nguyên linkTo hoặc tạo trang quản lý riêng
          openai: '/integrations/ai/openai/manage',
          anthropic: '/integrations/ai/anthropic/manage',
          gemini: '/integrations/ai/gemini/manage',
          deepseek: '/integrations/ai/deepseek/manage',

          'xai-grok': '/integrations/ai/xai-grok/manage',
          'twilio-sms': '/integrations/sms/twilio/manage',
          'fpt-sms': '/integrations/sms/fpt/manage',
          'email-smtp': '/integrations/email-servers',
          gmail: '/integrations/gmail/manage',
          'google-calendar': '/integrations/calendar/google/manage',
          facebook: '/integrations/facebook/manage',
          'facebook-ads': '/integrations/facebook-ads/manage',
          'zalo-oa': '/integrations/social/zalo-oa/manage',
          shipping: '/integrations/shipping',
        };

        return {
          ...item,
          linkTo: managementLinkMap[item.id] || item.linkTo,
        };
      })
      .filter((item): item is IntegrationItem => item !== null);

    const bankIntegrationCards: IntegrationItem[] = [
      {
        id: 'mb-bank-accounts',
        title: t('integration:bankAccount.mb.title', 'MB Banking'),
        description: t(
          'integration:bankAccount.mb.description',
          'Danh sách tài khoản MB Bank đã tích hợp'
        ),
        icon: 'mb-bank',
        linkTo: '/integrations/bank-accounts/mb',
        category: 'bank',
        gradientColor: 'success',
      },
      {
        id: 'acb-bank-accounts',
        title: t('integration:bankAccount.acb.title', 'ACB Banking'),
        description: t(
          'integration:bankAccount.acb.description',
          'Danh sách tài khoản ACB Bank đã tích hợp'
        ),
        icon: 'acb-bank',
        linkTo: '/integrations/bank-accounts/acb',
        category: 'bank',
        gradientColor: 'primary',
      },
      {
        id: 'ocb-bank-accounts',
        title: t('integration:bankAccount.ocb.title', 'OCB Banking'),
        description: t(
          'integration:bankAccount.ocb.description',
          'Danh sách tài khoản OCB Bank đã tích hợp'
        ),
        icon: 'ocb-bank',
        linkTo: '/integrations/bank-accounts/ocb',
        category: 'bank',
        gradientColor: 'info',
      },
      {
        id: 'kienlong-bank-accounts',
        title: t('integration:bankAccount.kienlong.title', 'Kiên Long Banking'),
        description: t(
          'integration:bankAccount.kienlong.description',
          'Danh sách tài khoản Kiên Long Bank đã tích hợp'
        ),
        icon: 'kienlong-bank',
        linkTo: '/integrations/bank-accounts/kienlong',
        category: 'bank',
        gradientColor: 'warning',
      },
      {
        id: 'open ai',
        title: t('integration:openai.title', 'OpenAI'),
        description: t('integration:openai.description', 'Danh sách API keys OpenAI đã tích hợp'),
        icon: 'openai',
        linkTo: '/integrations/ai/openai/manage',
        category: 'llm',
        gradientColor: 'success',
      },
      {
        id: 'anthropic',
        title: t('integration:anthropic.title', 'Anthropic Claude'),
        description: t(
          'integration:anthropic.description',
          'Danh sách API keys Anthropic Claude đã tích hợp'
        ),
        icon: 'anthropic',
        linkTo: '/integrations/ai/anthropic/manage',
        category: 'llm',
        gradientColor: 'primary',
      },
      {
        id: 'google',
        title: t('integration:gemini.title', 'Google Gemini'),
        description: t(
          'integration:gemini.description',
          'Danh sách API keys Google Gemini đã tích hợp'
        ),
        icon: 'gemini',
        linkTo: '/integrations/ai/gemini/manage',
        category: 'llm',
        gradientColor: 'primary',
      },
      {
        id: 'deepseek',
        title: t('integration:deepseek.title', 'DeepSeek'),
        description: t(
          'integration:deepseek.description',
          'Danh sách API keys DeepSeek đã tích hợp'
        ),
        icon: 'deepseek',
        linkTo: '/integrations/ai/deepseek/manage',
        category: 'llm',
        gradientColor: 'info',
      },

      {
        id: 'xai',
        title: t('integration:xaiGrok.title', 'XAI Grok'),
        description: t(
          'integration:xaiGrok.description',
          'Danh sách API keys XAI Grok đã tích hợp'
        ),
        icon: 'grok',
        linkTo: '/integrations/ai/xai-grok/manage',
        category: 'llm',
        gradientColor: 'error',
      },
      {
        id: 'shipping',
        title: t('integration:shipping.title', 'Vận chuyển'),
        description: t(
          'integration:shipping.description',
          'Danh sách các nhà cung cấp vận chuyển đã tích hợp'
        ),
        icon: 'truck',
        linkTo: '/integrations/shipping',
        category: 'shipping',
        gradientColor: 'primary',
      },

      {
        id: 'zalo-oa',
        title: 'Zalo OA',
        description: t(
          'integration:cards.social.zalo.description',
          'Danh sách các Zalo Official Account đã tích hợp'
        ),
        icon: 'zaloIcon',
        linkTo: '/integrations/social/zalo-oa/management',
        category: 'social',
        gradientColor: 'info',
      },
      {
        id: 'gmail',
        title: 'Gmail',
        description: t(
          'integration:cards.email.gmail.description',
          'Danh sách các tài khoản Gmail đã tích hợp'
        ),
        icon: 'google-gmail',
        linkTo: '/integrations/gmail/manage',
        category: 'email',
        gradientColor: 'primary',
      },
      {
        id: 'facebook-manager',
        title: 'Facebook',
        description: t(
          'integration:cards.social.facebook.description',
          'Danh sách các tài khoản Facebook đã tích hợp'
        ),
        icon: 'facebook',
        linkTo: '/integrations/facebook/manage',
        category: 'social',
        gradientColor: 'primary',
      },
      
    ];

    // Kết hợp base integrations với bank account cards
    return [...baseIntegrations, ...bankIntegrationCards];
  }, [userIntegrationTypes, mapIntegrationTypeToItem, t]);

  return {
    integrations: userIntegratedIntegrations,
    isLoading,
  };
};
