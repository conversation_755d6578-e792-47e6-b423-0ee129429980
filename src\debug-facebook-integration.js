// Debug script để kiểm tra Facebook integration data
// Ch<PERSON>y trong browser console trê<PERSON> trang "T<PERSON><PERSON> hợp của tôi"

console.log('🔍 Debug Facebook Integration Data');

// 1. Kiểm tra localStorage
console.log('📦 LocalStorage data:');
Object.keys(localStorage).forEach(key => {
  if (key.includes('facebook') || key.includes('integration')) {
    console.log(`  ${key}:`, localStorage.getItem(key));
  }
});

// 2. Kiểm tra sessionStorage
console.log('📦 SessionStorage data:');
Object.keys(sessionStorage).forEach(key => {
  if (key.includes('facebook') || key.includes('integration')) {
    console.log(`  ${key}:`, sessionStorage.getItem(key));
  }
});

// 3. Kiểm tra React Query cache
if (window.__REACT_QUERY_DEVTOOLS_GLOBAL_HOOK__) {
  console.log('🔄 React Query cache available');
  // <PERSON><PERSON> thể inspect cache qua DevTools
} else {
  console.log('❌ React Query DevTools not available');
}

// 4. Kiểm tra network requests
console.log('🌐 Monitoring network requests...');
const originalFetch = window.fetch;
window.fetch = function(...args) {
  const url = args[0];
  if (typeof url === 'string' && (url.includes('integration') || url.includes('facebook'))) {
    console.log('📡 API Request:', url);
    return originalFetch.apply(this, args).then(response => {
      console.log('📡 API Response:', url, response.status);
      return response.clone().json().then(data => {
        console.log('📡 API Data:', url, data);
        return response;
      }).catch(() => response);
    });
  }
  return originalFetch.apply(this, args);
};

// 5. Kiểm tra current URL và params
console.log('🔗 Current URL:', window.location.href);
console.log('🔗 Search params:', new URLSearchParams(window.location.search));

// 6. Hướng dẫn debug
console.log(`
🛠️  Debug Instructions:
1. Mở trang "Tích hợp của tôi": /integrations/my-integrations
2. Click vào Facebook card
3. Xem console logs để kiểm tra:
   - API calls được gọi
   - Data được trả về
   - Integration type được map
4. So sánh với expected values:
   - FACEBOOK_PAGE → /integrations/facebook/manage
   - FACEBOOK_ADS → /integrations/facebook-ads/manage
`);

// 7. Helper function để check integration mapping
window.debugFacebookIntegration = function() {
  console.log('🔍 Checking Facebook Integration Mapping...');
  
  // Simulate the mapping logic
  const IntegrationTypeEnum = {
    FACEBOOK_PAGE: 'FACEBOOK_PAGE',
    FACEBOOK_ADS: 'FACEBOOK_ADS'
  };
  
  const integrationTypeMap = {
    'facebook': IntegrationTypeEnum.FACEBOOK_PAGE,
    'facebook-ads': IntegrationTypeEnum.FACEBOOK_ADS,
  };
  
  const managementLinkMap = {
    'facebook': '/integrations/facebook/manage',
    'facebook-ads': '/integrations/facebook-ads/manage',
  };
  
  console.log('📋 Integration Type Map:', integrationTypeMap);
  console.log('📋 Management Link Map:', managementLinkMap);
  
  // Check what should happen
  console.log('✅ Expected behavior:');
  console.log('  - If backend returns FACEBOOK_PAGE → facebook → /integrations/facebook/manage');
  console.log('  - If backend returns FACEBOOK_ADS → facebook-ads → /integrations/facebook-ads/manage');
};

// Run the debug function
window.debugFacebookIntegration();
