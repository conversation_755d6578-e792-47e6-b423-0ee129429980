import { apiClient } from '@/shared/api';
import type { StorageStatistics } from '@/modules/data/types';

/**
 * API Integration Service cho Dashboard Widgets
 * Tích hợp tất cả APIs từ các modules khác vào dashboard
 */

// ==================== BUSINESS APIs ====================

export interface BusinessOverviewApiData {
  systemOverview: {
    totalRevenue: number;
    totalOrders: number;
    totalBusinesses: number;
    systemGrowthRate: number;
    averageOrderValue: number;
    conversionRate: number;
  };
  topBusinesses: Array<{
    id: number;
    name: string;
    revenue: number;
    orders: number;
  }>;
  distributionMetrics: {
    revenueByRegion: {
      north: number;
      south: number;
      central: number;
    };
    businessesBySize: {
      small: number;
      medium: number;
      large: number;
    };
  };
  generatedAt: string;
}

export interface BusinessSalesChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
  }[];
}

export interface BusinessKeyMetricsApiData {
  metrics: {
    averageOrderValue: number;
    conversionRate: number;
    customerRetentionRate: number;
    customerLifetimeValue: number;
    customerAcquisitionCost: number;
    grossProfitMargin: number;
    returnRate: number;
    revenueGrowthRate: number;
    orderGrowthRate: number;
    customerGrowthRate: number;
  };
  comparison: {
    previousPeriod: {
      averageOrderValue: number;
      conversionRate: number;
      customerRetentionRate: number;
    };
  };
  dateRange: {
    from: string;
    to: string;
  };
  period: string;
  generatedAt: string;
}

export interface TopBusinessesApiData {
  businesses: {
    id: number;
    name: string;
    revenue: number;
    orders: number;
    customers: number;
  }[];
  sortBy: string;
  dateRange: {
    from: string;
    to: string;
  };
  generatedAt: string;
}

export const BusinessApiService = {
  /**
   * Lấy tổng quan kinh doanh
   */
  getOverview: async (): Promise<BusinessOverviewApiData> => {
    const response = await apiClient.get<BusinessOverviewApiData>(
      '/admin/dashboard/business/overview'
    );
    return response.result;
  },

  /**
   * Lấy biểu đồ doanh số
   */
  getSalesChart: async (params?: {
    startDate?: string;
    endDate?: string;
    period?: string;
  }): Promise<BusinessSalesChartData> => {
    const response = await apiClient.get<BusinessSalesChartData>(
      '/admin/dashboard/business/reports/sales-chart',
      { params }
    );
    return response.result;
  },

  /**
   * Lấy thống kê đơn hàng
   */
  getOrdersStats: async (): Promise<{
    total: number;
    pending: number;
    completed: number;
    cancelled: number;
  }> => {
    const response = await apiClient.get('/admin/dashboard/business/orders/stats');
    return response.result as {
      total: number;
      pending: number;
      completed: number;
      cancelled: number;
    };
  },

  /**
   * Lấy key metrics của hệ thống business
   */
  getKeyMetrics: async (): Promise<BusinessKeyMetricsApiData> => {
    const response = await apiClient.get<BusinessKeyMetricsApiData>(
      '/admin/dashboard/business/key-metrics'
    );
    return response.result;
  },

  /**
   * Lấy danh sách top businesses
   */
  getTopBusinesses: async (params?: {
    sortBy?: string;
    limit?: number;
    startDate?: string;
    endDate?: string;
  }): Promise<TopBusinessesApiData> => {
    const response = await apiClient.get<TopBusinessesApiData>(
      '/admin/dashboard/business/top-businesses',
      { params }
    );
    return response.result;
  },
};

// ==================== EMPLOYEE APIs ====================

export interface EmployeeOverviewApiData {
  overview: {
    totalEmployees: number;
    newEmployees: number;
    activeEmployees: number;
    inactiveEmployees: number;
    verifiedEmployees: number;
    unverifiedEmployees: number;
  };
  activity: {
    totalLogins: number;
    uniqueLogins: number;
    averageSessionDuration: number;
    totalPageViews: number;
    bounceRate: number;
    returnVisitorRate: number;
  };
  performance: {
    employeeGrowthRate: number;
    loginGrowthRate: number;
    engagementGrowthRate: number;
    retentionRate: number;
    averagePerformanceScore: number;
  };
  departmentDistribution: Array<{
    departmentName: string;
    employeeCount: number;
    percentage: number;
  }>;
  roleDistribution: Array<{
    roleName: string;
    employeeCount: number;
    percentage: number;
  }>;
  dateRange: {
    from: string;
    to: string;
  };
  generatedAt: string;
}

export interface EmployeeActivityApiData {
  totalLogins: number;
  uniqueLogins: number;
  averageSessionDuration: number;
  totalPageViews: number;
  bounceRate: number;
  returnVisitorRate: number;
}

export interface EmployeeChartDataPoint {
  date: string;
  value: number;
  label: string;
}

export interface EmployeeChartApiData {
  data: EmployeeChartDataPoint[];
}

export const EmployeeApiService = {
  /**
   * Lấy tổng quan nhân viên
   */
  getOverview: async (): Promise<EmployeeOverviewApiData> => {
    const response = await apiClient.get<EmployeeOverviewApiData>(
      '/admin/dashboard/employees/overview'
    );
    return response.result;
  },

  /**
   * Lấy thống kê employee activity
   */
  getActivity: async (params?: {
    dateFrom?: string;
    dateTo?: string;
    departmentId?: number;
  }): Promise<EmployeeActivityApiData> => {
    const response = await apiClient.get<EmployeeActivityApiData>(
      '/admin/dashboard/employees/activity',
      { params }
    );
    return response.result;
  },

  /**
   * Lấy biểu đồ active employees theo thời gian
   */
  getActiveEmployeesChart: async (params?: {
    type?: string;
    fromDate?: string;
    toDate?: string;
  }): Promise<EmployeeChartApiData> => {
    const response = await apiClient.get<EmployeeChartApiData>(
      '/admin/dashboard/employees/charts/active-employees',
      { params }
    );
    return response.result;
  },

  /**
   * Lấy biểu đồ employee logins theo thời gian
   */
  getLoginsChart: async (params?: {
    type?: string;
    fromDate?: string;
    toDate?: string;
  }): Promise<EmployeeChartApiData> => {
    const response = await apiClient.get<EmployeeChartApiData>(
      '/admin/dashboard/employees/charts/logins',
      { params }
    );
    return response.result;
  },
};

// ==================== DATA APIs ====================

export interface DataOverviewStats {
  totalMedia: number;
  totalKnowledgeFiles: number;
  totalUrls: number;
}

export const DataApiService = {
  /**
   * Lấy thống kê tổng quan data
   */
  getOverview: async (): Promise<DataOverviewStats> => {
    const response = await apiClient.get<DataOverviewStats>('/admin/dashboard/data/statistics');
    return response.result;
  },

  /**
   * Lấy thống kê dung lượng
   */
  getStorageStats: async (): Promise<StorageStatistics> => {
    const response = await apiClient.get<StorageStatistics>('/admin/statistics/storage');
    return response.result;
  },
};

// ==================== MARKETING APIs ====================

export interface MarketingOverviewStats {
  totalContacts: number;
  totalSegments: number;
  activeCampaigns: number;
  totalCampaigns: number;
  customFieldsCount: number;
  tagsCount: number;
  emailTemplatesCount: number;
  smsCampaignsCount: number;
  googleAdsAccounts: number;
  facebookAdsAccounts: number;
  zaloOaAccounts: number;
}

export interface CampaignPerformanceData {
  totalSent: number;
  delivered: number;
  opened: number;
  clicked: number;
  bounced: number;
  unsubscribed: number;
  openRate: number;
  clickRate: number;
  bounceRate: number;
  unsubscribeRate: number;
}

export const MarketingApiService = {
  /**
   * Lấy tổng quan marketing
   */
  getOverview: async (): Promise<MarketingOverviewStats> => {
    const response = await apiClient.get<MarketingOverviewStats>('/marketing/statistics/overview');
    return response.result;
  },

  /**
   * Lấy hiệu suất chiến dịch
   */
  getCampaignPerformance: async (params?: {
    campaignId?: string;
    period?: string;
  }): Promise<CampaignPerformanceData> => {
    const response = await apiClient.get<CampaignPerformanceData>(
      '/marketing/campaigns/performance',
      { params }
    );
    return response.result;
  },
};

// ==================== AGENT APIs ====================

export interface AgentOverviewStats {
  totalAgents: number;
  activeAgents: number;
  inactiveAgents: number;
  totalConversations: number;
  totalMessages: number;
  averageResponseTime: number;
  successRate: number;
}

export interface AgentPerformanceData {
  agentId: string;
  agentName: string;
  conversations: number;
  messages: number;
  responseTime: number;
  successRate: number;
  uptime: number;
}

export const AgentApiService = {
  /**
   * Lấy tổng quan agents
   */
  getOverview: async (): Promise<AgentOverviewStats> => {
    const response = await apiClient.get<AgentOverviewStats>('/admin/agents/statistics');
    return response.result;
  },

  /**
   * Lấy hiệu suất agents
   */
  getPerformance: async (params?: {
    agentId?: string;
    period?: string;
  }): Promise<AgentPerformanceData[]> => {
    const response = await apiClient.get<AgentPerformanceData[]>('/admin/agents/performance', {
      params,
    });
    return response.result;
  },

  /**
   * Lấy hiệu suất external agents
   */
  getExternalAgentPerformance: async (agentId: string): Promise<AgentPerformanceData> => {
    const response = await apiClient.get<AgentPerformanceData>(
      `/api/external-agents/${agentId}/performance`
    );
    return response.result;
  },
};

// ==================== AFFILIATE APIs ====================

export interface AffiliateOverviewData {
  stats: {
    totalEarnings: number;
    pendingEarnings: number;
    totalReferrals: number;
    activeReferrals: number;
    conversionRate: number;
    clickCount: number;
  };
  recentActivity: {
    date: string;
    type: string;
    amount: number;
    description: string;
  }[];
}

export const AffiliateApiService = {
  /**
   * Lấy tổng quan affiliate
   */
  getOverview: async (): Promise<AffiliateOverviewData> => {
    const response = await apiClient.get<AffiliateOverviewData>('/admin/affiliate/overview');
    return response.result;
  },

  /**
   * Lấy thống kê affiliate
   */
  getStatistics: async (): Promise<any> => {
    const response = await apiClient.get('/admin/affiliate/statistics/overview');
    return response.result;
  },
};

// ==================== INTEGRATION APIs ====================

export interface IntegrationOverviewStats {
  total: number;
  active: number;
  inactive: number;
  pending: number;
  error: number;
  byType: Record<string, number>;
}

export const IntegrationApiService = {
  /**
   * Lấy thống kê tích hợp
   */
  getOverview: async (): Promise<IntegrationOverviewStats> => {
    const response = await apiClient.get<IntegrationOverviewStats>('/admin/integration/stats');
    return response.result;
  },

  /**
   * Lấy danh sách tích hợp
   */
  getIntegrations: async (): Promise<any[]> => {
    const response = await apiClient.get('/admin/integration');
    return response.result as any[];
  },
};

// ==================== ERROR HANDLING ====================

export class DashboardApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public endpoint?: string
  ) {
    super(message);
    this.name = 'DashboardApiError';
  }
}

/**
 * Wrapper function với error handling
 */
export const withErrorHandling = async <T>(
  apiCall: () => Promise<T>,
  endpoint: string
): Promise<T> => {
  try {
    return await apiCall();
  } catch (error: any) {
    console.error(`Dashboard API Error [${endpoint}]:`, error);

    const statusCode = error?.response?.status;
    const message = error?.response?.data?.message || error?.message || 'Unknown error';

    throw new DashboardApiError(message, statusCode, endpoint);
  }
};
