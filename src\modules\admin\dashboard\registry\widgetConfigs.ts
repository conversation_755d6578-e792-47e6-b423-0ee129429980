import React from 'react';
import {
  Users,
  DollarSign,
  BarChart3,
  Activity,
  Target,
  Zap,
  Globe,
  Building2,
  Type,
  ImageIcon,
  Clock,
  Quote,
  Code,
  ExternalLink,
  Play,
  Cloud,
  Calendar,
  CheckSquare,
  FileText,
  StickyNote,
  Timer,
  Calculator,
  Bookmark,
  Monitor,
  Bell,
  BarChart2,
  Bitcoin,
  Hash,
  UserCheck,
  LogIn,
} from 'lucide-react';
import { type WidgetConfig } from '../types';
import { WIDGET_TYPES, WIDGET_CATEGORIES } from '../constants';

// Sử dụng dynamic imports với proper default export handling
const BusinessOverviewWidget = React.lazy(() =>
  import('../components/business/BusinessOverviewWidget').then(module => ({
    default: module.default,
  }))
);
const EmployeeOverviewWidget = React.lazy(() =>
  import('../components/employee/EmployeeOverviewWidget').then(module => ({
    default: module.default,
  }))
);

const EmployeeActivityWidget = React.lazy(() =>
  import('../components/employee/EmployeeActivityWidget').then(module => ({
    default: module.default,
  }))
);

const EmployeeActiveChart = React.lazy(() =>
  import('../components/employee/EmployeeActiveChart').then(module => ({
    default: module.default,
  }))
);

const EmployeeLoginsChart = React.lazy(() =>
  import('../components/employee/EmployeeLoginsChart').then(module => ({
    default: module.default,
  }))
);
const BusinessKeyMetricsWidget = React.lazy(() =>
  import('../components/business/BusinessKeyMetricsWidget').then(module => ({
    default: module.default,
  }))
);
const TopBusinessesWidget = React.lazy(() =>
  import('../components/business/TopBusinessesWidget').then(module => ({
    default: module.default,
  }))
);

const MarketingOverviewWidget = React.lazy(() =>
  import('../components/marketing/MarketingOverviewWidget').then(module => ({
    default: module.default,
  }))
);
const CampaignPerformanceWidget = React.lazy(() =>
  import('../components/marketing/CampaignPerformanceWidget').then(module => ({
    default: module.default,
  }))
);
const AgentOverviewWidget = React.lazy(() =>
  import('../components/ai-agents/AgentOverviewWidget').then(module => ({
    default: module.default,
  }))
);
const AgentPerformanceWidget = React.lazy(() =>
  import('../components/ai-agents/AgentPerformanceWidget').then(module => ({
    default: module.default,
  }))
);
const AffiliateOverviewWidget = React.lazy(() =>
  import('../components/affiliate/AffiliateOverviewWidget').then(module => ({
    default: module.default,
  }))
);
const IntegrationOverviewWidget = React.lazy(() =>
  import('../components/integration/IntegrationOverviewWidget').then(module => ({
    default: module.default,
  }))
);

// Content widgets
const TextWidget = React.lazy(() =>
  import('../components/content/TextWidget').then(module => ({
    default: module.default,
  }))
);
const ImageWidget = React.lazy(() =>
  import('../components/content/ImageWidget').then(module => ({
    default: module.default,
  }))
);
const ClockWidget = React.lazy(() =>
  import('../components/content/ClockWidget').then(module => ({
    default: module.default,
  }))
);
const QuoteWidget = React.lazy(() =>
  import('../components/content/QuoteWidget').then(module => ({
    default: module.default,
  }))
);
const HTMLWidget = React.lazy(() =>
  import('../components/content/HTMLWidget').then(module => ({
    default: module.default,
  }))
);
const IframeWidget = React.lazy(() =>
  import('../components/content/IframeWidget').then(module => ({
    default: module.default,
  }))
);
const VideoWidget = React.lazy(() =>
  import('../components/content/VideoWidget').then(module => ({
    default: module.default,
  }))
);
const CounterWidget = React.lazy(() =>
  import('../components/content/CounterWidget').then(module => ({
    default: module.default,
  }))
);
const WeatherWidget = React.lazy(() =>
  import('../components/content/WeatherWidget').then(module => ({
    default: module.default,
  }))
);
const ProgressWidget = React.lazy(() =>
  import('../components/content/ProgressWidget').then(module => ({
    default: module.default,
  }))
);
const CalendarWidget = React.lazy(() =>
  import('../components/content/CalendarWidget').then(module => ({
    default: module.default,
  }))
);
const TodoWidget = React.lazy(() =>
  import('../components/content/TodoWidget').then(module => ({
    default: module.default,
  }))
);
const CVBuilderWidget = React.lazy(() =>
  import('../components/cv-builder/CVBuilderWidget').then(module => ({
    default: module.default,
  }))
);

// Productivity widgets
const NotesWidget = React.lazy(() =>
  import('../components/productivity/NotesWidget').then(module => ({
    default: module.default,
  }))
);
const TimerWidget = React.lazy(() =>
  import('../components/productivity/TimerWidget').then(module => ({
    default: module.default,
  }))
);
const CalculatorWidget = React.lazy(() =>
  import('../components/productivity/CalculatorWidget').then(module => ({
    default: module.default,
  }))
);
const BookmarkWidget = React.lazy(() =>
  import('../components/productivity/BookmarkWidget').then(module => ({
    default: module.default,
  }))
);

// System widgets
const SystemStatusWidget = React.lazy(() =>
  import('../components/system/SystemStatusWidget').then(module => ({
    default: module.default,
  }))
);

// Finance widgets
const CryptoPriceWidget = React.lazy(() =>
  import('../components/finance/CryptoPriceWidget').then(module => ({
    default: module.default,
  }))
);

// Social widgets
const NotificationWidget = React.lazy(() =>
  import('../components/social/NotificationWidget').then(module => ({
    default: module.default,
  }))
);

// Analytics widgets
const AnalyticsOverviewWidget = React.lazy(() =>
  import('../components/analytics/AnalyticsOverviewWidget').then(module => ({
    default: module.default,
  }))
);

/**
 * Widget configurations cho auto-registration
 * Mỗi widget được định nghĩa với đầy đủ metadata và dependencies
 */
export const WIDGET_CONFIGS: WidgetConfig[] = [
  // Business Widgets
  {
    id: 'business-overview-widget',
    type: WIDGET_TYPES.BUSINESS_OVERVIEW,
    category: WIDGET_CATEGORIES.BUSINESS,
    title: 'Tổng quan kinh doanh',
    description: 'Hiển thị các chỉ số kinh doanh quan trọng',
    icon: DollarSign,
    defaultSize: {
      w: 12,
      h: 4,
      minW: 8,
      minH: 3,
      maxW: 12,
      maxH: 6,
    },
    component: BusinessOverviewWidget,
    dependencies: ['orders-api', 'products-api', 'customers-api'],
    permissions: ['business:read'],
  },
  {
    id: 'business-key-metrics-widget',
    type: WIDGET_TYPES.BUSINESS_KEY_METRICS,
    category: WIDGET_CATEGORIES.BUSINESS,
    title: 'Chỉ số kinh doanh chính',
    description: 'Hiển thị các chỉ số kinh doanh quan trọng như AOV, LTV, CAC',
    icon: BarChart3,
    defaultSize: {
      w: 12,
      h: 6,
      minW: 10,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: BusinessKeyMetricsWidget,
    dependencies: ['business-metrics-api'],
    permissions: ['business:read'],
  },
  {
    id: 'top-businesses-widget',
    type: WIDGET_TYPES.TOP_BUSINESSES,
    category: WIDGET_CATEGORIES.BUSINESS,
    title: 'Top Businesses',
    description: 'Hiển thị danh sách các business hàng đầu theo doanh thu',
    icon: Building2,
    defaultSize: {
      w: 6,
      h: 8,
      minW: 4,
      minH: 6,
      maxW: 8,
      maxH: 12,
    },
    component: TopBusinessesWidget,
    dependencies: ['top-businesses-api'],
    permissions: ['business:read'],
  },
  {
    id: 'employee-overview-widget',
    type: WIDGET_TYPES.EMPLOYEE_OVERVIEW,
    category: WIDGET_CATEGORIES.EMPLOYEE,
    title: 'Tổng quan nhân viên',
    description: 'Hiển thị thống kê tổng quan về nhân viên trong hệ thống',
    icon: Users,
    defaultSize: {
      w: 12,
      h: 8,
      minW: 8,
      minH: 6,
      maxW: 12,
      maxH: 10,
    },
    component: EmployeeOverviewWidget,
    dependencies: ['employee-overview-api'],
    permissions: ['employee:read'],
  },
  {
    id: 'employee-activity-widget',
    type: WIDGET_TYPES.EMPLOYEE_ACTIVITY,
    category: WIDGET_CATEGORIES.EMPLOYEE,
    title: 'Hoạt động nhân viên',
    description: 'Thống kê chi tiết về hoạt động và tương tác của nhân viên',
    icon: Activity,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 6,
      minH: 5,
      maxW: 12,
      maxH: 8,
    },
    component: EmployeeActivityWidget,
    dependencies: ['employee-activity-api'],
    permissions: ['employee:read'],
  },
  {
    id: 'employee-active-chart-widget',
    type: WIDGET_TYPES.EMPLOYEE_ACTIVE_CHART,
    category: WIDGET_CATEGORIES.EMPLOYEE,
    title: 'Biểu đồ nhân viên hoạt động',
    description: 'Biểu đồ thể hiện số lượng nhân viên hoạt động theo thời gian',
    icon: UserCheck,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 6,
      minH: 5,
      maxW: 12,
      maxH: 8,
    },
    component: EmployeeActiveChart,
    dependencies: ['employee-active-chart-api'],
    permissions: ['employee:read'],
  },
  {
    id: 'employee-logins-chart-widget',
    type: WIDGET_TYPES.EMPLOYEE_LOGINS_CHART,
    category: WIDGET_CATEGORIES.EMPLOYEE,
    title: 'Biểu đồ lượt đăng nhập',
    description: 'Biểu đồ thống kê lượt đăng nhập của nhân viên theo thời gian',
    icon: LogIn,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 6,
      minH: 5,
      maxW: 12,
      maxH: 8,
    },
    component: EmployeeLoginsChart,
    dependencies: ['employee-logins-chart-api'],
    permissions: ['employee:read'],
  },

  // Marketing Widgets
  {
    id: 'marketing-overview-widget',
    type: WIDGET_TYPES.MARKETING_OVERVIEW,
    category: WIDGET_CATEGORIES.MARKETING,
    title: 'Tổng quan Marketing',
    description: 'Hiển thị tổng quan về các hoạt động marketing',
    icon: Target,
    defaultSize: {
      w: 12,
      h: 5,
      minW: 8,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: MarketingOverviewWidget,
    dependencies: ['marketing-api'],
    permissions: ['marketing:read'],
  },
  {
    id: 'campaign-performance-widget',
    type: WIDGET_TYPES.CAMPAIGN_PERFORMANCE,
    category: WIDGET_CATEGORIES.MARKETING,
    title: 'Hiệu suất chiến dịch',
    description: 'Hiển thị hiệu suất của các chiến dịch marketing',
    icon: BarChart3,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: CampaignPerformanceWidget,
    dependencies: ['campaigns-api'],
    permissions: ['marketing:read'],
  },

  // AI Agents Widgets
  {
    id: 'agent-overview-widget',
    type: WIDGET_TYPES.AGENT_OVERVIEW,
    category: WIDGET_CATEGORIES.AI_AGENTS,
    title: 'Tổng quan AI Agents',
    description: 'Hiển thị tổng quan về các AI agents',
    icon: Zap,
    defaultSize: {
      w: 8,
      h: 5,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: AgentOverviewWidget,
    dependencies: ['agents-api'],
    permissions: ['ai-agents:read'],
  },
  {
    id: 'agent-performance-widget',
    type: WIDGET_TYPES.AGENT_PERFORMANCE,
    category: WIDGET_CATEGORIES.AI_AGENTS,
    title: 'Hiệu suất Agents',
    description: 'Hiển thị hiệu suất hoạt động của AI agents',
    icon: Activity,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: AgentPerformanceWidget,
    dependencies: ['agents-performance-api'],
    permissions: ['ai-agents:read'],
  },

  // Affiliate Widgets
  {
    id: 'affiliate-overview-widget',
    type: WIDGET_TYPES.AFFILIATE_OVERVIEW,
    category: WIDGET_CATEGORIES.AFFILIATE,
    title: 'Tổng quan Affiliate',
    description: 'Hiển thị tổng quan về hệ thống affiliate',
    icon: Users,
    defaultSize: {
      w: 8,
      h: 5,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: AffiliateOverviewWidget,
    dependencies: ['affiliate-api'],
    permissions: ['affiliate:read'],
  },

  // Integration Widgets
  {
    id: 'integration-overview-widget',
    type: WIDGET_TYPES.INTEGRATION_OVERVIEW,
    category: WIDGET_CATEGORIES.INTEGRATION,
    title: 'Tổng quan tích hợp',
    description: 'Hiển thị trạng thái các tích hợp hệ thống',
    icon: Globe,
    defaultSize: {
      w: 8,
      h: 5,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 8,
    },
    component: IntegrationOverviewWidget,
    dependencies: ['integrations-api'],
    permissions: ['integrations:read'],
  },

  // Content Widgets
  {
    id: 'text-widget',
    type: WIDGET_TYPES.TEXT_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget văn bản',
    description: 'Hiển thị văn bản tùy chỉnh có thể chỉnh sửa',
    icon: Type,
    defaultSize: {
      w: 6,
      h: 4,
      minW: 3,
      minH: 2,
      maxW: 12,
      maxH: 8,
    },
    component: TextWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'image-widget',
    type: WIDGET_TYPES.IMAGE_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget hình ảnh',
    description: 'Hiển thị hình ảnh từ URL với các tùy chọn hiển thị',
    icon: ImageIcon,
    defaultSize: {
      w: 6,
      h: 6,
      minW: 3,
      minH: 3,
      maxW: 12,
      maxH: 12,
    },
    component: ImageWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'clock-widget',
    type: WIDGET_TYPES.CLOCK_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget đồng hồ',
    description: 'Hiển thị đồng hồ thời gian thực với múi giờ tùy chỉnh',
    icon: Clock,
    defaultSize: {
      w: 4,
      h: 3,
      minW: 3,
      minH: 2,
      maxW: 8,
      maxH: 6,
    },
    component: ClockWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'quote-widget',
    type: WIDGET_TYPES.QUOTE_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget trích dẫn',
    description: 'Hiển thị trích dẫn truyền cảm hứng với tác giả',
    icon: Quote,
    defaultSize: {
      w: 8,
      h: 4,
      minW: 4,
      minH: 3,
      maxW: 12,
      maxH: 8,
    },
    component: QuoteWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'html-widget',
    type: WIDGET_TYPES.HTML_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget HTML',
    description: 'Hiển thị HTML tùy chỉnh với sandbox bảo mật',
    icon: Code,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 4,
      minH: 4,
      maxW: 12,
      maxH: 12,
    },
    component: HTMLWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'iframe-widget',
    type: WIDGET_TYPES.IFRAME_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget iframe',
    description: 'Nhúng trang web hoặc ứng dụng bên ngoài',
    icon: ExternalLink,
    defaultSize: {
      w: 8,
      h: 8,
      minW: 4,
      minH: 4,
      maxW: 12,
      maxH: 12,
    },
    component: IframeWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'video-widget',
    type: WIDGET_TYPES.VIDEO_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget video',
    description: 'Nhúng video từ YouTube, Vimeo hoặc URL trực tiếp',
    icon: Play,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 4,
      minH: 3,
      maxW: 12,
      maxH: 10,
    },
    component: VideoWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'counter-widget',
    type: WIDGET_TYPES.COUNTER_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget đếm số',
    description: 'Hiển thị số đếm với animation và customization',
    icon: Hash,
    defaultSize: {
      w: 4,
      h: 3,
      minW: 3,
      minH: 2,
      maxW: 8,
      maxH: 6,
    },
    component: CounterWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'weather-widget',
    type: WIDGET_TYPES.WEATHER_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget thời tiết',
    description: 'Hiển thị thông tin thời tiết và dự báo',
    icon: Cloud,
    defaultSize: {
      w: 6,
      h: 5,
      minW: 4,
      minH: 4,
      maxW: 10,
      maxH: 8,
    },
    component: WeatherWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'progress-widget',
    type: WIDGET_TYPES.PROGRESS_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget tiến độ',
    description: 'Hiển thị progress bars với animation',
    icon: BarChart3,
    defaultSize: {
      w: 6,
      h: 4,
      minW: 4,
      minH: 3,
      maxW: 12,
      maxH: 8,
    },
    component: ProgressWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'calendar-widget',
    type: WIDGET_TYPES.CALENDAR_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget lịch',
    description: 'Lịch với event highlighting và date picker',
    icon: Calendar,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 6,
      minH: 5,
      maxW: 12,
      maxH: 10,
    },
    component: CalendarWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'todo-widget',
    type: WIDGET_TYPES.TODO_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'Widget todo list',
    description: 'Quản lý task với add/remove/check functionality',
    icon: CheckSquare,
    defaultSize: {
      w: 6,
      h: 6,
      minW: 4,
      minH: 4,
      maxW: 10,
      maxH: 12,
    },
    component: TodoWidget,
    dependencies: [],
    permissions: [],
  },

  {
    id: 'cv-builder-widget',
    type: WIDGET_TYPES.CV_BUILDER_WIDGET,
    category: WIDGET_CATEGORIES.CONTENT,
    title: 'CV Builder',
    description: 'Tạo và chỉnh sửa CV chuyên nghiệp với WYSIWYG editor',
    icon: FileText,
    defaultSize: {
      w: 12,
      h: 8,
      minW: 10,
      minH: 6,
      maxW: 12,
      maxH: 12,
    },
    component: CVBuilderWidget,
    dependencies: [],
    permissions: [],
  },

  // Productivity Widgets
  {
    id: 'notes-widget',
    type: WIDGET_TYPES.NOTES_WIDGET,
    category: WIDGET_CATEGORIES.PRODUCTIVITY,
    title: 'Widget ghi chú',
    description: 'Ghi chú nhanh với khả năng thêm, sửa, xóa',
    icon: StickyNote,
    defaultSize: {
      w: 6,
      h: 6,
      minW: 4,
      minH: 4,
      maxW: 12,
      maxH: 12,
    },
    component: NotesWidget,
    dependencies: [],
    permissions: [],
  },
  {
    id: 'timer-widget',
    type: WIDGET_TYPES.TIMER_WIDGET,
    category: WIDGET_CATEGORIES.PRODUCTIVITY,
    title: 'Widget đồng hồ bấm giờ',
    description: 'Pomodoro timer với thông báo và theo dõi phiên làm việc',
    icon: Timer,
    defaultSize: {
      w: 4,
      h: 5,
      minW: 3,
      minH: 4,
      maxW: 8,
      maxH: 8,
    },
    component: TimerWidget,
    dependencies: [],
    permissions: [],
  },
  {
    id: 'calculator-widget',
    type: WIDGET_TYPES.CALCULATOR_WIDGET,
    category: WIDGET_CATEGORIES.PRODUCTIVITY,
    title: 'Widget máy tính',
    description: 'Máy tính đơn giản với các phép toán cơ bản',
    icon: Calculator,
    defaultSize: {
      w: 4,
      h: 6,
      minW: 3,
      minH: 5,
      maxW: 6,
      maxH: 8,
    },
    component: CalculatorWidget,
    dependencies: [],
    permissions: [],
  },
  {
    id: 'bookmark-widget',
    type: WIDGET_TYPES.BOOKMARK_WIDGET,
    category: WIDGET_CATEGORIES.PRODUCTIVITY,
    title: 'Widget bookmark',
    description: 'Quản lý bookmark với favicon và mở link nhanh',
    icon: Bookmark,
    defaultSize: {
      w: 6,
      h: 6,
      minW: 4,
      minH: 4,
      maxW: 12,
      maxH: 12,
    },
    component: BookmarkWidget,
    dependencies: [],
    permissions: [],
  },

  // System Widgets
  {
    id: 'system-status-widget',
    type: WIDGET_TYPES.SYSTEM_STATUS_WIDGET,
    category: WIDGET_CATEGORIES.SYSTEM,
    title: 'Trạng thái hệ thống',
    description: 'Hiển thị trạng thái server, CPU, RAM, disk và network',
    icon: Monitor,
    defaultSize: {
      w: 8,
      h: 6,
      minW: 6,
      minH: 5,
      maxW: 12,
      maxH: 10,
    },
    component: SystemStatusWidget,
    dependencies: [],
    permissions: [],
  },

  // Finance Widgets
  {
    id: 'crypto-price-widget',
    type: WIDGET_TYPES.CRYPTO_PRICE_WIDGET,
    category: WIDGET_CATEGORIES.FINANCE,
    title: 'Giá cryptocurrency',
    description: 'Theo dõi giá Bitcoin, Ethereum và các coin khác',
    icon: Bitcoin,
    defaultSize: {
      w: 6,
      h: 6,
      minW: 4,
      minH: 5,
      maxW: 10,
      maxH: 10,
    },
    component: CryptoPriceWidget,
    dependencies: [],
    permissions: [],
  },

  // Social Widgets
  {
    id: 'notification-widget',
    type: WIDGET_TYPES.NOTIFICATION_WIDGET,
    category: WIDGET_CATEGORIES.SOCIAL,
    title: 'Widget thông báo',
    description: 'Hiển thị thông báo realtime với đánh dấu đã đọc',
    icon: Bell,
    defaultSize: {
      w: 6,
      h: 6,
      minW: 4,
      minH: 5,
      maxW: 10,
      maxH: 12,
    },
    component: NotificationWidget,
    dependencies: [],
    permissions: [],
  },

  // Analytics Widgets
  {
    id: 'analytics-overview-widget',
    type: WIDGET_TYPES.ANALYTICS_OVERVIEW_WIDGET,
    category: WIDGET_CATEGORIES.ANALYTICS,
    title: 'Tổng quan Analytics',
    description: 'Hiển thị metrics website: lượt xem, người dùng, bounce rate',
    icon: BarChart2,
    defaultSize: {
      w: 8,
      h: 7,
      minW: 6,
      minH: 6,
      maxW: 12,
      maxH: 10,
    },
    component: AnalyticsOverviewWidget,
    dependencies: [],
    permissions: [],
  },
];

/**
 * Get widget config by type
 */
export const getWidgetConfig = (type: string): WidgetConfig | undefined => {
  return WIDGET_CONFIGS.find(config => config.type === type);
};

/**
 * Get widgets by category
 */
export const getWidgetsByCategory = (category: string): WidgetConfig[] => {
  return WIDGET_CONFIGS.filter(config => config.category === category);
};

/**
 * Get all available widget types
 */
export const getAvailableWidgetTypes = (): string[] => {
  return WIDGET_CONFIGS.map(config => config.type);
};

export default WIDGET_CONFIGS;
