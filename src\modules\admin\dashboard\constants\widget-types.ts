/**
 * Widget type constants for dashboard components
 * Centralized widget type definitions for type safety and consistency
 */

export const WIDGET_TYPES = {
  // Marketing widgets
  MARKETING_OVERVIEW: 'marketing-overview',
  CAMPAIGN_PERFORMANCE: 'campaign-performance',
  // AI Agents widgets
  AGENT_OVERVIEW: 'agent-overview',
  AGENT_PERFORMANCE: 'agent-performance',
  // Business widgets
  BUSINESS_OVERVIEW: 'business-overview',
  BUSINESS_KEY_METRICS: 'business-key-metrics',
  TOP_BUSINESSES: 'top-businesses',
  // Employee widgets
  EMPLOYEE_OVERVIEW: 'employee-overview',
  EMPLOYEE_ACTIVITY: 'employee-activity',
  EMPLOYEE_ACTIVE_CHART: 'employee-active-chart',
  EMPLOYEE_LOGINS_CHART: 'employee-logins-chart',

  // Affiliate widgets
  AFFILIATE_OVERVIEW: 'affiliate-overview',
  // Integration widgets
  INTEGRATION_OVERVIEW: 'integration-overview',
  // Simple content widgets
  TEXT_WIDGET: 'text-widget',
  IMAGE_WIDGET: 'image-widget',
  VIDEO_WIDGET: 'video-widget',
  HTML_WIDGET: 'html-widget',
  IFRAME_WIDGET: 'iframe-widget',
  CLOCK_WIDGET: 'clock-widget',
  WEATHER_WIDGET: 'weather-widget',
  QUOTE_WIDGET: 'quote-widget',
  COUNTER_WIDGET: 'counter-widget',
  PROGRESS_WIDGET: 'progress-widget',
  CALENDAR_WIDGET: 'calendar-widget',
  TODO_WIDGET: 'todo-widget',

  TABLE_WIDGET: 'table-widget',

  CV_BUILDER_WIDGET: 'cv-builder-widget',
  // Productivity widgets
  NOTES_WIDGET: 'notes-widget',
  TIMER_WIDGET: 'timer-widget',
  CALCULATOR_WIDGET: 'calculator-widget',
  BOOKMARK_WIDGET: 'bookmark-widget',
  // System widgets
  SYSTEM_STATUS_WIDGET: 'system-status-widget',
  SERVER_MONITOR_WIDGET: 'server-monitor-widget',
  DATABASE_STATUS_WIDGET: 'database-status-widget',
  API_HEALTH_WIDGET: 'api-health-widget',
  // Finance widgets
  CRYPTO_PRICE_WIDGET: 'crypto-price-widget',
  STOCK_PRICE_WIDGET: 'stock-price-widget',
  EXPENSE_TRACKER_WIDGET: 'expense-tracker-widget',
  INVOICE_WIDGET: 'invoice-widget',
  // Social widgets
  SOCIAL_MEDIA_WIDGET: 'social-media-widget',
  NOTIFICATION_WIDGET: 'notification-widget',
  CHAT_WIDGET: 'chat-widget',
  FEEDBACK_WIDGET: 'feedback-widget',
  // Analytics widgets
  ANALYTICS_OVERVIEW_WIDGET: 'analytics-overview-widget',
  TRAFFIC_SOURCE_WIDGET: 'traffic-source-widget',
  CONVERSION_FUNNEL_WIDGET: 'conversion-funnel-widget',
  HEATMAP_WIDGET: 'heatmap-widget',
} as const;

/**
 * Widget type union type derived from WIDGET_TYPES constants
 */
export type WidgetType = (typeof WIDGET_TYPES)[keyof typeof WIDGET_TYPES];

/**
 * Widget type validation helper
 */
export const isValidWidgetType = (type: string): type is WidgetType => {
  return Object.values(WIDGET_TYPES).includes(type as WidgetType);
};

/**
 * Get widget type display name
 */
export const getWidgetTypeDisplayName = (type: WidgetType): string => {
  const displayNames: Record<WidgetType, string> = {
    [WIDGET_TYPES.MARKETING_OVERVIEW]: 'Tổng quan Marketing',
    [WIDGET_TYPES.CAMPAIGN_PERFORMANCE]: 'Hiệu suất chiến dịch',
    [WIDGET_TYPES.AGENT_OVERVIEW]: 'Tổng quan AI Agents',
    [WIDGET_TYPES.AGENT_PERFORMANCE]: 'Hiệu suất Agents',
    [WIDGET_TYPES.BUSINESS_OVERVIEW]: 'Tổng quan kinh doanh',
    [WIDGET_TYPES.BUSINESS_KEY_METRICS]: 'Chỉ số kinh doanh chính',
    [WIDGET_TYPES.TOP_BUSINESSES]: 'Top Businesses',
    [WIDGET_TYPES.EMPLOYEE_OVERVIEW]: 'Tổng quan nhân viên',

    [WIDGET_TYPES.AFFILIATE_OVERVIEW]: 'Tổng quan Affiliate',
    [WIDGET_TYPES.INTEGRATION_OVERVIEW]: 'Tổng quan tích hợp',
    [WIDGET_TYPES.TEXT_WIDGET]: 'Widget văn bản',
    [WIDGET_TYPES.IMAGE_WIDGET]: 'Widget hình ảnh',
    [WIDGET_TYPES.VIDEO_WIDGET]: 'Widget video',
    [WIDGET_TYPES.HTML_WIDGET]: 'Widget HTML',
    [WIDGET_TYPES.IFRAME_WIDGET]: 'Widget iframe',
    [WIDGET_TYPES.CLOCK_WIDGET]: 'Widget đồng hồ',
    [WIDGET_TYPES.WEATHER_WIDGET]: 'Widget thời tiết',
    [WIDGET_TYPES.QUOTE_WIDGET]: 'Widget trích dẫn',
    [WIDGET_TYPES.COUNTER_WIDGET]: 'Widget đếm số',
    [WIDGET_TYPES.PROGRESS_WIDGET]: 'Widget tiến độ',
    [WIDGET_TYPES.CALENDAR_WIDGET]: 'Widget lịch',
    [WIDGET_TYPES.TODO_WIDGET]: 'Widget todo list',

    [WIDGET_TYPES.TABLE_WIDGET]: 'Widget bảng dữ liệu',

    [WIDGET_TYPES.CV_BUILDER_WIDGET]: 'CV Builder',
    // Productivity widgets
    [WIDGET_TYPES.NOTES_WIDGET]: 'Widget ghi chú',
    [WIDGET_TYPES.TIMER_WIDGET]: 'Widget đồng hồ bấm giờ',
    [WIDGET_TYPES.CALCULATOR_WIDGET]: 'Widget máy tính',
    [WIDGET_TYPES.BOOKMARK_WIDGET]: 'Widget bookmark',
    // System widgets
    [WIDGET_TYPES.SYSTEM_STATUS_WIDGET]: 'Trạng thái hệ thống',
    [WIDGET_TYPES.SERVER_MONITOR_WIDGET]: 'Monitor server',
    [WIDGET_TYPES.DATABASE_STATUS_WIDGET]: 'Trạng thái database',
    [WIDGET_TYPES.API_HEALTH_WIDGET]: 'Health check API',
    // Finance widgets
    [WIDGET_TYPES.CRYPTO_PRICE_WIDGET]: 'Giá cryptocurrency',
    [WIDGET_TYPES.STOCK_PRICE_WIDGET]: 'Giá cổ phiếu',
    [WIDGET_TYPES.EXPENSE_TRACKER_WIDGET]: 'Theo dõi chi phí',
    [WIDGET_TYPES.INVOICE_WIDGET]: 'Quản lý hóa đơn',
    // Social widgets
    [WIDGET_TYPES.SOCIAL_MEDIA_WIDGET]: 'Widget mạng xã hội',
    [WIDGET_TYPES.NOTIFICATION_WIDGET]: 'Widget thông báo',
    [WIDGET_TYPES.CHAT_WIDGET]: 'Widget chat',
    [WIDGET_TYPES.FEEDBACK_WIDGET]: 'Widget phản hồi',
    // Analytics widgets
    [WIDGET_TYPES.ANALYTICS_OVERVIEW_WIDGET]: 'Tổng quan Analytics',
    [WIDGET_TYPES.TRAFFIC_SOURCE_WIDGET]: 'Nguồn traffic',
    [WIDGET_TYPES.CONVERSION_FUNNEL_WIDGET]: 'Phễu chuyển đổi',
    [WIDGET_TYPES.HEATMAP_WIDGET]: 'Widget heatmap',
  };

  return displayNames[type] || 'Không xác định';
};

/**
 * Widget type categories for grouping
 */
export const WIDGET_CATEGORIES = {
  DATA: 'data',
  VISUALIZATION: 'visualization',
  MARKETING: 'marketing',
  AI_AGENTS: 'ai-agents',
  BUSINESS: 'business',
  EMPLOYEE: 'employee',
  AFFILIATE: 'affiliate',
  INTEGRATION: 'integration',
  CONTENT: 'content',
  PRODUCTIVITY: 'productivity',
  SYSTEM: 'system',
  FINANCE: 'finance',
  SOCIAL: 'social',
  ANALYTICS: 'analytics',
} as const;

export type WidgetCategory = (typeof WIDGET_CATEGORIES)[keyof typeof WIDGET_CATEGORIES];

/**
 * Map widget types to categories
 */
export const WIDGET_TYPE_CATEGORIES: Record<WidgetType, WidgetCategory> = {
  [WIDGET_TYPES.MARKETING_OVERVIEW]: WIDGET_CATEGORIES.MARKETING,
  [WIDGET_TYPES.CAMPAIGN_PERFORMANCE]: WIDGET_CATEGORIES.MARKETING,
  [WIDGET_TYPES.AGENT_OVERVIEW]: WIDGET_CATEGORIES.AI_AGENTS,
  [WIDGET_TYPES.AGENT_PERFORMANCE]: WIDGET_CATEGORIES.AI_AGENTS,
  [WIDGET_TYPES.BUSINESS_OVERVIEW]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.BUSINESS_KEY_METRICS]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.TOP_BUSINESSES]: WIDGET_CATEGORIES.BUSINESS,
  [WIDGET_TYPES.EMPLOYEE_OVERVIEW]: WIDGET_CATEGORIES.EMPLOYEE,
  [WIDGET_TYPES.EMPLOYEE_ACTIVITY]: WIDGET_CATEGORIES.EMPLOYEE,
  [WIDGET_TYPES.EMPLOYEE_ACTIVE_CHART]: WIDGET_CATEGORIES.EMPLOYEE,
  [WIDGET_TYPES.EMPLOYEE_LOGINS_CHART]: WIDGET_CATEGORIES.EMPLOYEE,

  [WIDGET_TYPES.AFFILIATE_OVERVIEW]: WIDGET_CATEGORIES.AFFILIATE,
  [WIDGET_TYPES.INTEGRATION_OVERVIEW]: WIDGET_CATEGORIES.INTEGRATION,
  [WIDGET_TYPES.TEXT_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.IMAGE_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.VIDEO_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.HTML_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.IFRAME_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.CLOCK_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.WEATHER_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.QUOTE_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.COUNTER_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.PROGRESS_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.CALENDAR_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  [WIDGET_TYPES.TODO_WIDGET]: WIDGET_CATEGORIES.CONTENT,

  [WIDGET_TYPES.TABLE_WIDGET]: WIDGET_CATEGORIES.DATA,

  [WIDGET_TYPES.CV_BUILDER_WIDGET]: WIDGET_CATEGORIES.CONTENT,
  // Productivity widgets
  [WIDGET_TYPES.NOTES_WIDGET]: WIDGET_CATEGORIES.PRODUCTIVITY,
  [WIDGET_TYPES.TIMER_WIDGET]: WIDGET_CATEGORIES.PRODUCTIVITY,
  [WIDGET_TYPES.CALCULATOR_WIDGET]: WIDGET_CATEGORIES.PRODUCTIVITY,
  [WIDGET_TYPES.BOOKMARK_WIDGET]: WIDGET_CATEGORIES.PRODUCTIVITY,
  // System widgets
  [WIDGET_TYPES.SYSTEM_STATUS_WIDGET]: WIDGET_CATEGORIES.SYSTEM,
  [WIDGET_TYPES.SERVER_MONITOR_WIDGET]: WIDGET_CATEGORIES.SYSTEM,
  [WIDGET_TYPES.DATABASE_STATUS_WIDGET]: WIDGET_CATEGORIES.SYSTEM,
  [WIDGET_TYPES.API_HEALTH_WIDGET]: WIDGET_CATEGORIES.SYSTEM,
  // Finance widgets
  [WIDGET_TYPES.CRYPTO_PRICE_WIDGET]: WIDGET_CATEGORIES.FINANCE,
  [WIDGET_TYPES.STOCK_PRICE_WIDGET]: WIDGET_CATEGORIES.FINANCE,
  [WIDGET_TYPES.EXPENSE_TRACKER_WIDGET]: WIDGET_CATEGORIES.FINANCE,
  [WIDGET_TYPES.INVOICE_WIDGET]: WIDGET_CATEGORIES.FINANCE,
  // Social widgets
  [WIDGET_TYPES.SOCIAL_MEDIA_WIDGET]: WIDGET_CATEGORIES.SOCIAL,
  [WIDGET_TYPES.NOTIFICATION_WIDGET]: WIDGET_CATEGORIES.SOCIAL,
  [WIDGET_TYPES.CHAT_WIDGET]: WIDGET_CATEGORIES.SOCIAL,
  [WIDGET_TYPES.FEEDBACK_WIDGET]: WIDGET_CATEGORIES.SOCIAL,
  // Analytics widgets
  [WIDGET_TYPES.ANALYTICS_OVERVIEW_WIDGET]: WIDGET_CATEGORIES.ANALYTICS,
  [WIDGET_TYPES.TRAFFIC_SOURCE_WIDGET]: WIDGET_CATEGORIES.ANALYTICS,
  [WIDGET_TYPES.CONVERSION_FUNNEL_WIDGET]: WIDGET_CATEGORIES.ANALYTICS,
  [WIDGET_TYPES.HEATMAP_WIDGET]: WIDGET_CATEGORIES.ANALYTICS,
};
