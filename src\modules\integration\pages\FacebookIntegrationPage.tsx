import { Button, EmptyState, Icon, Loading, Typography } from '@/shared/components/common';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { useCreateFacebookAuthUrl, useHandleFacebookCallback } from '../facebook/hooks/useFacebook';
import PageWrapper from '@/shared/components/common/PageWrapper';
import { useNavigate } from 'react-router-dom';

/**
 * Trang tích hợp Facebook mới
 */
const FacebookIntegrationPage: React.FC = () => {
  const { t } = useTranslation('integration');
  const { success, error: showError } = useSmartNotification();
  const navigate = useNavigate();
  const [isConnecting, setIsConnecting] = useState(false);

  // Get current URL for redirect
  const currentUrl = window.location.pathname;

  // Facebook Auth hooks
  const authUrlQuery = useCreateFacebookAuthUrl({ endpointCallback: currentUrl });
  const handleCallbackMutation = useHandleFacebookCallback();

  // Handle Facebook callback when page loads with code parameter
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');

    if (code && state && !handleCallbackMutation.isPending) {
      setIsConnecting(true);

      handleCallbackMutation.mutate(
        {
          code,
          endpointCallback: currentUrl,
          state,
        },
        {
          onSuccess: () => {
            // Remove code from URL
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete('code');
            newUrl.searchParams.delete('state');
            window.history.replaceState({}, '', newUrl.toString());

            setIsConnecting(false);

            // Hiển thị thông báo thành công
            success({
              title: t('integration.facebook.connectSuccess', 'Thành công'),
              message: t(
                'integration.facebook.connectSuccessMessage',
                'Đã kết nối Facebook thành công! Bạn có thể quản lý các trang Facebook đã tích hợp.'
              ),
              duration: 3000,
            });

            // Chuyển hướng đến trang quản lý Facebook sau 2 giây
            setTimeout(() => {
              navigate('/integrations/facebook/manage');
            }, 2000);
          },
          onError: error => {
            console.error('Facebook callback error:', error);
            setIsConnecting(false);

            // Hiển thị thông báo lỗi
            showError({
              title: t('integration.facebook.connectError', 'Lỗi kết nối'),
              message:
                error instanceof Error
                  ? error.message
                  : t(
                      'integration.facebook.connectErrorMessage',
                      'Có lỗi xảy ra khi kết nối Facebook'
                    ),
              duration: 5000,
            });
          },
        }
      );
    }
  }, [handleCallbackMutation, currentUrl, setIsConnecting, success, showError, t, navigate]);

  // Handle connect Facebook
  const handleConnectFacebook = async () => {
    try {
      setIsConnecting(true);

      // Check if we already have auth URL data
      if (authUrlQuery.data?.result?.authUrl) {
        // Redirect to Facebook auth
        window.location.href = authUrlQuery.data.result.authUrl;
      } else {
        // Fetch auth URL
        const authResponse = await authUrlQuery.refetch();

        if (authResponse.data?.result?.authUrl) {
          // Redirect to Facebook auth
          window.location.href = authResponse.data.result.authUrl;
        } else {
          console.error('No auth URL received');
          setIsConnecting(false);

          showError({
            title: t('integration.facebook.connectError', 'Lỗi kết nối'),
            message: t('integration.facebook.authUrlError', 'Không thể lấy URL xác thực Facebook'),
            duration: 5000,
          });
        }
      }
    } catch (error) {
      console.error('Error getting Facebook auth URL:', error);
      setIsConnecting(false);

      showError({
        title: t('integration.facebook.connectError', 'Lỗi kết nối'),
        message:
          error instanceof Error
            ? error.message
            : t('integration.facebook.connectErrorMessage', 'Có lỗi xảy ra khi kết nối Facebook'),
        duration: 5000,
      });
    }
  };

  return (
    <PageWrapper className="px-4 sm:px-6 lg:px-8">
      {isConnecting ? (
        <div className="flex flex-col justify-center items-center py-12 sm:py-16">
          <Loading size="lg" />
          <Typography className="mt-3 text-sm sm:text-base text-center">
            {t('integration.facebook.processing', 'Đang xử lý kết nối Facebook...')}
          </Typography>
        </div>
      ) : (
        <EmptyState
          icon="facebook"
          title={t('integration.facebook.connectTitle', 'Tích hợp Facebook')}
          description={t(
            'integration.facebook.connectDescription',
            'Kết nối tài khoản Facebook của bạn để bắt đầu quản lý các trang Facebook và sử dụng các tính năng tích hợp.'
          )}
          actions={
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                variant="outline"
                onClick={() => navigate('/integrations/facebook/manage')}
                className="flex items-center justify-center space-x-2"
              >
                <Icon name="list" size="sm" />
                <span>{t('integration.facebook.managePages', 'Quản lý trang đã tích hợp')}</span>
              </Button>
              <Button
                variant="primary"
                onClick={handleConnectFacebook}
                isLoading={isConnecting}
                disabled={isConnecting}
                className="flex items-center justify-center space-x-2"
              >
                <Icon name="plus" size="sm" />
                <span>
                  {isConnecting
                    ? t('integration.facebook.connecting', 'Đang kết nối...')
                    : t('integration.facebook.connectFacebook', 'Kết nối Facebook')}
                </span>
              </Button>
            </div>
          }
        />
      )}
    </PageWrapper>
  );
};

export default FacebookIntegrationPage;
