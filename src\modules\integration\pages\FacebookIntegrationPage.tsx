import { But<PERSON>, Card, Icon, Loading, Typography } from '@/shared/components/common';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { useCreateFacebookAuthUrl, useHandleFacebookCallback } from '../facebook/hooks/useFacebook';
import PageWrapper from '@/shared/components/common/PageWrapper';
import { useNavigate } from 'react-router-dom';

/**
 * Trang tích hợp Facebook mới
 */
const FacebookIntegrationPage: React.FC = () => {
  const { t } = useTranslation('integration');
  const { success, error: showError } = useSmartNotification();
  const navigate = useNavigate();
  const [isConnecting, setIsConnecting] = useState(false);

  // Get current URL for redirect
  const currentUrl = window.location.pathname;

  // Facebook Auth hooks
  const authUrlQuery = useCreateFacebookAuthUrl({ endpointCallback: currentUrl });
  const handleCallbackMutation = useHandleFacebookCallback();

  // Handle Facebook callback when page loads with code parameter
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');

    if (code && state && !handleCallbackMutation.isPending) {
      setIsConnecting(true);

      handleCallbackMutation.mutate(
        {
          code,
          endpointCallback: currentUrl,
          state,
        },
        {
          onSuccess: () => {
            // Remove code from URL
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete('code');
            newUrl.searchParams.delete('state');
            window.history.replaceState({}, '', newUrl.toString());

            setIsConnecting(false);

            // Hiển thị thông báo thành công
            success({
              title: t('integration.facebook.connectSuccess', 'Thành công'),
              message: t(
                'integration.facebook.connectSuccessMessage',
                'Đã kết nối Facebook thành công! Bạn có thể quản lý các trang Facebook đã tích hợp.'
              ),
              duration: 3000,
            });

            // Chuyển hướng đến trang quản lý Facebook sau 2 giây
            setTimeout(() => {
              navigate('/integrations/facebook/manage');
            }, 2000);
          },
          onError: error => {
            console.error('Facebook callback error:', error);
            setIsConnecting(false);

            // Hiển thị thông báo lỗi
            showError({
              title: t('integration.facebook.connectError', 'Lỗi kết nối'),
              message:
                error instanceof Error
                  ? error.message
                  : t(
                      'integration.facebook.connectErrorMessage',
                      'Có lỗi xảy ra khi kết nối Facebook'
                    ),
              duration: 5000,
            });
          },
        }
      );
    }
  }, [handleCallbackMutation, currentUrl, setIsConnecting, success, showError, t, navigate]);

  // Handle connect Facebook
  const handleConnectFacebook = async () => {
    try {
      setIsConnecting(true);

      // Check if we already have auth URL data
      if (authUrlQuery.data?.result?.authUrl) {
        // Redirect to Facebook auth
        window.location.href = authUrlQuery.data.result.authUrl;
      } else {
        // Fetch auth URL
        const authResponse = await authUrlQuery.refetch();

        if (authResponse.data?.result?.authUrl) {
          // Redirect to Facebook auth
          window.location.href = authResponse.data.result.authUrl;
        } else {
          console.error('No auth URL received');
          setIsConnecting(false);

          showError({
            title: t('integration.facebook.connectError', 'Lỗi kết nối'),
            message: t('integration.facebook.authUrlError', 'Không thể lấy URL xác thực Facebook'),
            duration: 5000,
          });
        }
      }
    } catch (error) {
      console.error('Error getting Facebook auth URL:', error);
      setIsConnecting(false);

      showError({
        title: t('integration.facebook.connectError', 'Lỗi kết nối'),
        message:
          error instanceof Error
            ? error.message
            : t('integration.facebook.connectErrorMessage', 'Có lỗi xảy ra khi kết nối Facebook'),
        duration: 5000,
      });
    }
  };

  return (
    <PageWrapper className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
        {isConnecting ? (
          <div className="flex flex-col justify-center items-center py-16 sm:py-24">
            <div className="relative">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Icon name="facebook" size="lg" className="text-white" />
              </div>
              <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur opacity-25 animate-pulse"></div>
            </div>
            <Loading size="lg" className="mt-6" />
            <Typography className="mt-4 text-lg font-medium text-gray-700 text-center">
              {t('integration.facebook.processing', 'Đang xử lý kết nối Facebook...')}
            </Typography>
            <Typography className="mt-2 text-sm text-gray-500 text-center max-w-md">
              {t(
                'integration.facebook.processingDescription',
                'Vui lòng đợi trong giây lát, chúng tôi đang thiết lập kết nối với Facebook.'
              )}
            </Typography>
          </div>
        ) : (
          <div className="text-center">
            {/* Main Integration Card */}
            <div className="max-w-lg mx-auto">
              <Card className="p-8 sm:p-10 shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
                <div className="space-y-6">
                  {/* Facebook Icon */}
                  <div className="relative">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto shadow-lg">
                      <Icon name="facebook" size="lg" className="text-white" />
                    </div>
                    <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur opacity-25"></div>
                  </div>

                  {/* Connect Button */}
                  <Button
                    variant="primary"
                    onClick={handleConnectFacebook}
                    isLoading={isConnecting}
                    disabled={isConnecting}
                    className="w-full py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                  >
                    <Icon name="facebook" size="sm" className="mr-2" />
                    <span>
                      {isConnecting
                        ? t('integration.facebook.connecting', 'Đang kết nối...')
                        : t('integration.facebook.connectFacebook', 'Kết nối với Facebook')}
                    </span>
                  </Button>

                  {/* Security Note */}
                  <div className="flex items-center justify-center space-x-2 text-green-600 bg-green-50 py-2 px-4 rounded-lg">
                    <Icon name="shield-check" size="sm" />
                    <Typography className="text-sm font-medium">
                      {t(
                        'integration.facebook.securityNote',
                        'Kết nối được bảo mật bằng OAuth 2.0'
                      )}
                    </Typography>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}
      </div>
    </PageWrapper>
  );
};

export default FacebookIntegrationPage;
