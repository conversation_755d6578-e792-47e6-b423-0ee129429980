import React, { useState, useEffect } from 'react';
import { Card, Skeleton } from '@/shared/components/common';
import { Activity, Users, Clock, Eye, TrendingUp, TrendingDown } from 'lucide-react';
import { cn } from '@/shared/utils/cn';
import { EmployeeApiService, type EmployeeActivityApiData } from '../../services/api-integration.service';

interface EmployeeActivityWidgetProps {
  className?: string;
}

const EmployeeActivityWidget: React.FC<EmployeeActivityWidgetProps> = ({ className }) => {
  const [data, setData] = useState<EmployeeActivityApiData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('🔄 Fetching employee activity data...');
        
        const result = await EmployeeApiService.getActivity();
        console.log('✅ Employee Activity API Response data:', result);
        
        setData(result);
      } catch (err) {
        console.error('❌ Error fetching employee activity:', err);
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  const formatPercentage = (num: number): string => {
    return `${num.toFixed(1)}%`;
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  if (loading) {
    return (
      <Card 
        className={cn('h-full', className)}
        title={
          <div className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Hoạt động nhân viên
          </div>
        }
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton height="16px" width="120px" />
                <Skeleton height="24px" width="80px" />
              </div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card 
        className={cn('h-full', className)}
        title={
          <div className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Hoạt động nhân viên
          </div>
        }
      >
        <div className="flex items-center justify-center h-32 text-red-500">
          <p>Lỗi: {error}</p>
        </div>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card 
        className={cn('h-full', className)}
        title={
          <div className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Hoạt động nhân viên
          </div>
        }
      >
        <div className="flex items-center justify-center h-32 text-gray-500">
          <p>Không có dữ liệu</p>
        </div>
      </Card>
    );
  }

  return (
    <Card 
      className={cn('h-full', className)}
      title={
        <div className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Hoạt động nhân viên
        </div>
      }
    >
      <div className="space-y-6">
        {/* Login Stats */}
        <div>
          <h4 className="text-sm font-medium mb-3">Thống kê đăng nhập</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div>
                <p className="text-xs text-muted-foreground">Tổng đăng nhập</p>
                <p className="text-lg font-bold text-blue-600">
                  {formatNumber(data.totalLogins)}
                </p>
              </div>
              <Users className="h-5 w-5 text-blue-500" />
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div>
                <p className="text-xs text-muted-foreground">Đăng nhập duy nhất</p>
                <p className="text-lg font-bold text-green-600">
                  {formatNumber(data.uniqueLogins)}
                </p>
              </div>
              <Users className="h-5 w-5 text-green-500" />
            </div>
          </div>
        </div>

        {/* Session & Activity Stats */}
        <div>
          <h4 className="text-sm font-medium mb-3">Phiên làm việc</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
              <div>
                <p className="text-xs text-muted-foreground">Thời gian TB</p>
                <p className="text-lg font-bold text-purple-600">
                  {formatDuration(data.averageSessionDuration)}
                </p>
              </div>
              <Clock className="h-5 w-5 text-purple-500" />
            </div>
            <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
              <div>
                <p className="text-xs text-muted-foreground">Lượt xem trang</p>
                <p className="text-lg font-bold text-orange-600">
                  {formatNumber(data.totalPageViews)}
                </p>
              </div>
              <Eye className="h-5 w-5 text-orange-500" />
            </div>
          </div>
        </div>

        {/* Engagement Stats */}
        <div>
          <h4 className="text-sm font-medium mb-3">Tương tác</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <div>
                <p className="text-xs text-muted-foreground">Tỷ lệ thoát</p>
                <p className="text-lg font-bold text-red-600">
                  {formatPercentage(data.bounceRate)}
                </p>
              </div>
              <TrendingDown className="h-5 w-5 text-red-500" />
            </div>
            <div className="flex items-center justify-between p-3 bg-emerald-50 rounded-lg">
              <div>
                <p className="text-xs text-muted-foreground">Tỷ lệ quay lại</p>
                <p className="text-lg font-bold text-emerald-600">
                  {formatPercentage(data.returnVisitorRate)}
                </p>
              </div>
              <TrendingUp className="h-5 w-5 text-emerald-500" />
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default EmployeeActivityWidget;
