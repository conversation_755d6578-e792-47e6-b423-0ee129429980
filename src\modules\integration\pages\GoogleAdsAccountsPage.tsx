import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  Typography,
  Chip,
  Skeleton,
  ConfirmDeleteModal,
  ActionMenu,
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { GoogleAdsService } from '@/modules/integration/google-ads/services';
import { GoogleAdsIntegration, GoogleAdsQueryParams } from '@/modules/integration/google-ads/types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { formatDate } from '@/shared/utils/format';
import { useNavigate } from 'react-router-dom';

/**
 * Trang quản lý Google Ads Integrations
 */
const GoogleAdsAccountsPage: React.FC = () => {
  const { t } = useTranslation(['integration', 'common']);

  const navigate = useNavigate();
  const { success, error: showError } = useSmartNotification();

  // Data states
  const [googleAdsIntegrations, setGoogleAdsIntegrations] = useState<GoogleAdsIntegration[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalItems, setTotalItems] = useState(0);

  // Delete modal states
  const [integrationToDelete, setIntegrationToDelete] = useState<GoogleAdsIntegration | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [isReloading, setIsReloading] = useState(false);

  // Column visibility
  const [, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Ref để tránh multiple concurrent calls
  const isLoadingRef = useRef(false);

  // Load Google Ads integrations
  const loadGoogleAdsIntegrations = useCallback(async () => {
    if (isLoadingRef.current) return;

    try {
      isLoadingRef.current = true;
      setIsLoading(true);

      const params: GoogleAdsQueryParams = {
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined,
        sortBy: 'createdAt',
        sortDirection: 'DESC',
      };

      const response = await GoogleAdsService.getIntegrations(params);
      setGoogleAdsIntegrations(response.items || []);
      setTotalItems(response.meta?.totalItems || 0);
    } catch (error) {
      console.error('Failed to load Google Ads integrations:', error);
      setGoogleAdsIntegrations([]);
      setTotalItems(0);

      showError({
        message: t(
          'integration:googleAds.errors.loadFailed',
          'Không thể tải danh sách Google Ads integrations'
        ),
      });
    } finally {
      isLoadingRef.current = false;
      setIsLoading(false);
    }
  }, [currentPage, pageSize, searchTerm, t, showError]);

  const handleNavigateToCreate = () => {
    navigate('/integrations/google-ads');
  };

  // Delete integration
  const handleDelete = async () => {
    if (!integrationToDelete) return;

    try {
      setIsSubmitting(true);
      await GoogleAdsService.deleteIntegration(integrationToDelete.id);

      success({
        message: t(
          'integration:googleAds.messages.deleteSuccess',
          'Xóa Google Ads integration thành công'
        ),
      });

      await loadGoogleAdsIntegrations();
      setShowDeleteConfirm(false);
      setIntegrationToDelete(null);
    } catch (error) {
      console.error('Failed to delete Google Ads integration:', error);
      showError({
        message: t(
          'integration:googleAds.errors.deleteFailed',
          'Không thể xóa Google Ads integration'
        ),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show delete confirmation
  const handleShowDeleteConfirm = useCallback((integration: GoogleAdsIntegration) => {
    setIntegrationToDelete(integration);
    setShowDeleteConfirm(true);
  }, []);

  // View in Google Ads
  const handleViewInGoogleAds = useCallback((customerId: string) => {
    window.open(`https://ads.google.com/aw/accounts?ocid=${customerId}`, '_blank');
  }, []);

  // Table columns
  const columns = useMemo<TableColumn<GoogleAdsIntegration>[]>(
    () => [
      {
        key: 'integrationName',
        title: t('integration:googleAds.table.columns.name', 'Tên tích hợp'),
        dataIndex: 'integrationName',
        width: '25%',
        sortable: true,
      },
      {
        key: 'accountInfo',
        title: t('integration:googleAds.table.columns.account', 'Tài khoản'),
        dataIndex: 'accountInfo',
        width: '25%',
        render: (_, record: GoogleAdsIntegration) => (
          <div>
            <Typography variant="subtitle2" className="font-medium">
              {record.accountInfo?.accountName || 'N/A'}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground">
              ID: {record.accountInfo?.customerId || 'N/A'}
            </Typography>
          </div>
        ),
      },
      {
        key: 'userInfo',
        title: t('integration:googleAds.table.columns.email', 'Email'),
        dataIndex: 'userInfo',
        width: '20%',
        render: (_, record: GoogleAdsIntegration) => (
          <Typography variant="body2" className="text-muted-foreground">
            {record.userInfo?.email || 'N/A'}
          </Typography>
        ),
      },
      {
        key: 'currency',
        title: t('integration:googleAds.table.columns.currency', 'Tiền tệ'),
        dataIndex: 'accountInfo',
        width: '10%',
        render: (_, record: GoogleAdsIntegration) => (
          <Chip variant="info" size="sm">
            {record.accountInfo?.currency || 'N/A'}
          </Chip>
        ),
      },
      {
        key: 'isConnected',
        title: t('integration:googleAds.table.columns.status', 'Trạng thái'),
        dataIndex: 'isConnected',
        width: '15%',
        sortable: true,
        render: (_, record: GoogleAdsIntegration) => {
          const isConnected = record.isConnected;
          if (isConnected) {
            return (
              <Chip variant="success" size="sm">
                <CheckCircle className="h-3 w-3 mr-1" />
                {t('integration:googleAds.status.connected', 'Đã kết nối')}
              </Chip>
            );
          } else {
            return (
              <Chip variant="danger" size="sm">
                <XCircle className="h-3 w-3 mr-1" />
                {t('integration:googleAds.status.disconnected', 'Chưa kết nối')}
              </Chip>
            );
          }
        },
      },
      {
        key: 'lastSyncAt',
        title: t('integration:googleAds.table.columns.lastSync', 'Lần cuối đồng bộ'),
        dataIndex: 'usage',
        width: '15%',
        sortable: true,
        render: (_, record: GoogleAdsIntegration) => (
          <Typography variant="caption" className="text-muted-foreground">
            {record.usage?.lastSyncAt
              ? formatDate(new Date(record.usage.lastSyncAt), 'dd/MM/yyyy HH:mm')
              : 'Chưa đồng bộ'}
          </Typography>
        ),
      },
      {
        key: 'actions',
        title: t('common:actions', 'Hành động'),
        dataIndex: 'id',
        width: '10%',
        render: (_, record: GoogleAdsIntegration) => (
          <ActionMenu
            items={[
              {
                id: 'view',
                label: t('common:view', 'Xem'),
                icon: 'external-link',
                onClick: () => handleViewInGoogleAds(record.accountInfo?.customerId || ''),
              },
              {
                id: 'delete',
                label: t('common:delete', 'Xóa'),
                icon: 'trash',
                onClick: () => handleShowDeleteConfirm(record),
              },
            ]}
          />
        ),
      },
    ],
    [t, handleViewInGoogleAds, handleShowDeleteConfirm]
  );

  // Load data on mount
  useEffect(() => {
    loadGoogleAdsIntegrations();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Reload when pagination/search changes
  useEffect(() => {
    loadGoogleAdsIntegrations();
  }, [currentPage, pageSize, searchTerm]); // eslint-disable-line react-hooks/exhaustive-deps

  // Search handler
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
  }, []);

  // Reload handler
  const handleReload = useCallback(async () => {
    if (isReloading) return;

    setIsReloading(true);
    await loadGoogleAdsIntegrations();
    setIsReloading(false);
  }, [isReloading, loadGoogleAdsIntegrations]);

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  return (
    <div className="w-full bg-background text-foreground space-y-6 p-8">
      {/* Header */}
      <div className="mb-6">
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleNavigateToCreate}
          columns={columns.map(col => ({
            id: col.key,
            label: typeof col.title === 'string' ? col.title : col.key,
            visible: true,
          }))}
          onColumnVisibilityChange={setVisibleColumns}
          additionalIcons={[
            {
              icon: 'refresh-cw',
              tooltip: t('common:reload', 'Tải lại'),
              onClick: handleReload,
              condition: !isReloading,
            },
            {
              icon: 'loader',
              tooltip: t('common:loading', 'Đang tải...'),
              onClick: () => {},
              condition: isReloading,
            },
          ]}
        />
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('integration:googleAds.stats.total', 'Tổng tích hợp')}
              </Typography>
              <Typography variant="h2" className="text-blue-600">
                {isLoading ? <Skeleton className="h-8 w-12" /> : totalItems}
              </Typography>
            </div>
            <CheckCircle className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('integration:googleAds.stats.connected', 'Đã kết nối')}
              </Typography>
              <Typography variant="h2" className="text-green-600">
                {isLoading ? (
                  <Skeleton className="h-8 w-12" />
                ) : (
                  googleAdsIntegrations.filter(integration => integration.isConnected).length
                )}
              </Typography>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="caption" className="text-muted-foreground">
                {t('integration:googleAds.stats.disconnected', 'Chưa kết nối')}
              </Typography>
              <Typography variant="h2" className="text-orange-600">
                {isLoading ? (
                  <Skeleton className="h-8 w-12" />
                ) : (
                  googleAdsIntegrations.filter(integration => !integration.isConnected).length
                )}
              </Typography>
            </div>
            <AlertCircle className="h-8 w-8 text-orange-600" />
          </div>
        </Card>
      </div>

      {/* Table */}
      <Card>
        <Table
          columns={columns}
          data={googleAdsIntegrations}
          loading={isLoading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalItems,
            showSizeChanger: true,
            onChange: handlePageChange,
          }}
        />
      </Card>

      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleDelete}
        title={t('integration:googleAds.deleteConfirm.title', 'Xác nhận xóa')}
        message={
          integrationToDelete
            ? t(
                'integration:googleAds.deleteConfirm.message',
                'Bạn có chắc chắn muốn xóa tích hợp "{{name}}" không? Hành động này không thể hoàn tác.',
                { name: integrationToDelete.integrationName }
              )
            : ''
        }
        isSubmitting={isSubmitting}
      />
    </div>
  );
};

export default GoogleAdsAccountsPage;
