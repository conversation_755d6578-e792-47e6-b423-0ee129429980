import React, { useCallback, useMemo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { DashboardCollapsibleSidebar, DashboardWorkspace, AddWidgetModal } from '../components';
import DashboardExportModal from '../components/DashboardExportModal';
import { DashboardWidget } from '../types';
import { useDashboardTabs } from '../hooks/useDashboardTabs';
import { useDashboardPrint } from '../hooks/useDashboardPrint';
import { initializeWidgets } from '../registry/autoRegister';
import { debugWidgets } from '../utils/debugWidgets';
import { cleanupDashboardTabs } from '../utils/cleanupWidgets';
// import '../scripts/cleanupDashboard'; // Load cleanup scripts for debugging
import { usePDFExport } from '../hooks/usePDFExport';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { DashboardManagementService } from '../services/dashboard-management.service';
import '../styles/dashboard.css';

const DashboardPage: React.FC = () => {
  const { t } = useTranslation(['dashboard', 'common']);
  const { success, error } = useSmartNotification();
  const [isAddWidgetModalOpen, setIsAddWidgetModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [displayMode, setDisplayMode] = useState<'normal' | 'minimal'>('normal');
  const [smartLayoutMode, setSmartLayoutMode] = useState(true);
  const [forceRefresh, setForceRefresh] = useState(0); // State để force refresh

  const { isExporting } = usePDFExport();
  const { isPrinting } = useDashboardPrint();

  // Initialize widgets on component mount
  useEffect(() => {
    const initWidgets = async () => {
      try {
        await initializeWidgets();

        // Clean up invalid widgets from localStorage
        const hasCleanedUp = cleanupDashboardTabs();
        if (hasCleanedUp) {
          console.log('🧹 Dashboard cleanup completed - forcing refresh');
          // Thay thế page reload bằng force refresh state
          setForceRefresh(prev => prev + 1);
          return;
        }

        // Debug widget registry
        setTimeout(() => {
          debugWidgets.checkRegistry();
        }, 1000);
      } catch (error) {
        console.error('Failed to initialize dashboard widgets:', error);
      }
    };

    initWidgets();
  }, [forceRefresh]); // Thêm forceRefresh vào dependency để re-run khi cleanup

  // Use tab system with backend integration
  const {
    tabsState,
    currentTab,
    isInitialized,
    currentDashboardId,
    isLoading,
    switchToTab,
    createTab,
    renameTab,
    deleteTab,
    changeTabMode,
    addWidgetToCurrentTab,
    removeWidgetFromCurrentTab,
    updateWidgetTitleInCurrentTab,
    updateWidgetPropsInCurrentTab,
    updateTabWidgets,
    saveToServer,
    resetToDefault, // Thêm function để reset dashboard
  } = useDashboardTabs();

  // Reset dashboard khi có cleanup
  useEffect(() => {
    if (forceRefresh > 0) {
      resetToDefault?.();
    }
  }, [forceRefresh, resetToDefault]);

  // Get current tab widgets
  const widgets = useMemo(() => currentTab?.widgets || [], [currentTab?.widgets]);

  // Handle layout change
  const handleLayoutChange = useCallback(
    (layout: any[]) => {
      if (!currentTab) return;

      // Update widgets with new layout positions
      const updatedWidgets = widgets.map(widget => {
        const layoutItem = layout.find(item => item.i === widget.id);
        if (layoutItem) {
          return {
            ...widget,
            x: layoutItem.x,
            y: layoutItem.y,
            w: layoutItem.w,
            h: layoutItem.h,
          };
        }
        return widget;
      });

      // Update current tab widgets using the hook
      updateTabWidgets(updatedWidgets);
    },
    [currentTab, widgets, updateTabWidgets]
  );

  // Handle adding widget from modal
  const handleAddWidget = useCallback(
    (widget: DashboardWidget) => {
      addWidgetToCurrentTab(widget);
    },
    [addWidgetToCurrentTab]
  );

  const handleRemoveWidget = useCallback(
    (widgetId: string) => {
      // Only allow removing widgets in edit mode
      if (currentTab?.mode !== 'edit') {
        return;
      }
      removeWidgetFromCurrentTab(widgetId);
    },
    [currentTab?.mode, removeWidgetFromCurrentTab]
  );

  const handleSave = useCallback(async () => {
    try {
      // Save to server first (this will handle both create and update cases)
      if (currentDashboardId) {
        await saveToServer();
      } else {
        // Create new dashboard on server
        await saveToServer('My Dashboard');
      }

      // Save to localStorage as backup (without triggering API call)
      // Note: saveToStorage() also calls API, so we manually save to localStorage only
      try {
        DashboardManagementService.saveToLocalStorage(tabsState);
      } catch (localError) {
        console.warn('Failed to save to localStorage:', localError);
      }

      // Show success notification
      success({
        title: t('common:success'),
        message: t('dashboard:save.success', 'Dashboard đã được lưu thành công'),
      });
    } catch (saveError) {
      console.error('❌ Failed to save dashboard:', saveError);

      // Show error notification
      error({
        title: t('common:error'),
        message: t(
          'dashboard:save.error',
          'Có lỗi xảy ra khi lưu dashboard. Dữ liệu đã được lưu cục bộ.'
        ),
      });

      // Save to localStorage as fallback when server save fails
      try {
        DashboardManagementService.saveToLocalStorage(tabsState);
      } catch (localError) {
        console.warn('Failed to save to localStorage as fallback:', localError);
      }
    }
  }, [saveToServer, currentDashboardId, success, error, t, tabsState]);

  const handleToggleDisplayMode = useCallback(() => {
    setDisplayMode(prev => (prev === 'normal' ? 'minimal' : 'normal'));
  }, []);

  const handleToggleSmartLayout = useCallback(() => {
    setSmartLayoutMode(prev => {
      const newMode = !prev;

      // Force refresh để trigger smart layout optimization
      if (newMode) {
        setForceRefresh(prev => prev + 1);
      }

      return newMode;
    });
  }, []);

  const handleOpenExportModal = useCallback(() => {
    setIsExportModalOpen(true);
  }, []);

  const handleCloseExportModal = useCallback(() => {
    setIsExportModalOpen(false);
  }, []);

  // Show loading state while initializing
  if (isLoading && !isInitialized) {
    return (
      <div className="w-full bg-background text-foreground flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Đang tải dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-background text-foreground flex flex-col min-h-screen dashboard-page">
      {/* Horizontal Sidebar */}
      <DashboardCollapsibleSidebar
        onAddWidget={() => setIsAddWidgetModalOpen(true)}
        onCreateTab={createTab}
        canEdit={currentTab?.mode === 'edit'}
        tabs={tabsState.tabs}
        currentTabId={tabsState.currentTabId}
        currentTabMode={currentTab?.mode}
        onSwitchTab={switchToTab}
        onDeleteTab={deleteTab}
        onRenameTab={renameTab}
        onChangeMode={changeTabMode}
        onSave={handleSave}
        displayMode={displayMode}
        onToggleDisplayMode={handleToggleDisplayMode}
        onExportPDF={handleOpenExportModal}
        isExportingPDF={isExporting || isPrinting}
        smartLayoutMode={smartLayoutMode}
        onToggleSmartLayout={handleToggleSmartLayout}
      />

      {/* Main Workspace */}
      <div className="flex-1" data-dashboard-container>
        <DashboardWorkspace
          widgets={widgets}
          onLayoutChange={handleLayoutChange}
          onRemoveWidget={handleRemoveWidget}
          onWidgetTitleChange={updateWidgetTitleInCurrentTab}
          onWidgetPropsChange={updateWidgetPropsInCurrentTab}
          isDraggable={currentTab?.mode === 'edit'}
          isResizable={currentTab?.mode === 'edit'}
          mode={currentTab?.mode || 'edit'}
          displayMode={displayMode}
          smartLayoutMode={smartLayoutMode}
        />
      </div>

      {/* Add Widget Modal */}
      <AddWidgetModal
        isOpen={isAddWidgetModalOpen}
        onClose={() => setIsAddWidgetModalOpen(false)}
        onAddWidget={handleAddWidget}
        existingWidgets={widgets}
      />

      {/* Export Modal - Unified PDF & Print */}
      <DashboardExportModal isOpen={isExportModalOpen} onClose={handleCloseExportModal} />
    </div>
  );
};

export default DashboardPage;
