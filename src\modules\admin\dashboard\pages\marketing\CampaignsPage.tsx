import React from 'react';
import { DashboardCard } from '../../components';
import { DashboardWidget } from '../../types';

const CampaignsPage: React.FC = () => {
  const widgets: DashboardWidget[] = [];

  return (
    <div className="h-full">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-foreground">Chiến dịch Marketing</h1>
        <p className="text-muted-foreground mt-1">
          <PERSON> dõi hiệu suất và ROI của các chiến dịch marketing
        </p>
      </div>

      <DashboardCard widgets={widgets} isDraggable={true} isResizable={true} />
    </div>
  );
};

export default CampaignsPage;
