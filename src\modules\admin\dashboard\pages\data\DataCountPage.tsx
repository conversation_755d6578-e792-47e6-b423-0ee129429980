import React from 'react';
import { DashboardCard } from '../../components';
import { DashboardWidget } from '../../types';

/**
 * Trang hiển thị tổng số lượng dữ liệu
 */
const DataCountPage: React.FC = () => {
  const widgets: DashboardWidget[] = [];

  return (
    <div className="h-full">
      <DashboardCard widgets={widgets} isDraggable={true} isResizable={true} className="h-full" />
    </div>
  );
};

export default DataCountPage;
