import React from 'react';
import { DashboardCard } from '../../components';
import { DashboardWidget } from '../../types';

const ApiUsagePage: React.FC = () => {
  const widgets: DashboardWidget[] = [];

  return (
    <div className="h-full">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-foreground">Sử dụng API</h1>
        <p className="text-muted-foreground mt-1">
          Theo dõi việc sử dụng API và hiệu suất các endpoints
        </p>
      </div>

      <DashboardCard widgets={widgets} isDraggable={true} isResizable={true} />
    </div>
  );
};

export default ApiUsagePage;
