import React, { useState, useEffect } from 'react';
import { Card, Skeleton } from '@/shared/components/common';
import { UserCheck, TrendingUp } from 'lucide-react';
import { cn } from '@/shared/utils/cn';
import {
  EmployeeApiService,
  type EmployeeChartApiData,
} from '../../services/api-integration.service';
import {
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';

interface EmployeeActiveChartProps {
  className?: string;
}

const EmployeeActiveChart: React.FC<EmployeeActiveChartProps> = ({ className }) => {
  const [data, setData] = useState<EmployeeChartApiData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('🔄 Fetching active employees chart data...');

        const result = await EmployeeApiService.getActiveEmployeesChart({
          type: 'ORDER',
        });
        console.log('✅ Active Employees Chart API Response data:', result);

        setData(result);
      } catch (err) {
        console.error('❌ Error fetching active employees chart:', err);
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const formatTooltipValue = (value: number) => {
    return [`${value} nhân viên`, 'Số lượng'];
  };

  const formatXAxisLabel = (tickItem: string) => {
    try {
      const date = new Date(tickItem);
      return date.toLocaleDateString('vi-VN', {
        month: 'short',
        day: 'numeric',
      });
    } catch {
      return tickItem;
    }
  };

  if (loading) {
    return (
      <Card
        className={cn('h-full', className)}
        title={
          <div className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Nhân viên hoạt động
          </div>
        }
      >
        <div className="space-y-4">
          <Skeleton height="200px" width="100%" />
          <div className="flex justify-between">
            <Skeleton height="16px" width="80px" />
            <Skeleton height="16px" width="80px" />
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card
        className={cn('h-full', className)}
        title={
          <div className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Nhân viên hoạt động
          </div>
        }
      >
        <div className="flex items-center justify-center h-32 text-red-500">
          <p>Lỗi: {error}</p>
        </div>
      </Card>
    );
  }

  if (!data || !data.data || data.data.length === 0) {
    return (
      <Card
        className={cn('h-full', className)}
        title={
          <div className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Nhân viên hoạt động
          </div>
        }
      >
        <div className="flex items-center justify-center h-32 text-gray-500">
          <p>Không có dữ liệu biểu đồ</p>
        </div>
      </Card>
    );
  }

  // Tính toán thống kê
  const currentValue = data.data[data.data.length - 1]?.value || 0;
  const previousValue = data.data[data.data.length - 2]?.value || 0;
  const changePercent =
    previousValue > 0 ? ((currentValue - previousValue) / previousValue) * 100 : 0;
  const isPositive = changePercent >= 0;

  return (
    <Card
      className={cn('h-full', className)}
      title={
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Nhân viên hoạt động
          </div>
          <div className="flex items-center gap-2 text-sm">
            <span className="font-semibold">{currentValue}</span>
            <div
              className={cn(
                'flex items-center gap-1',
                isPositive ? 'text-green-600' : 'text-red-600'
              )}
            >
              <TrendingUp className={cn('h-3 w-3', !isPositive && 'rotate-180')} />
              <span>{Math.abs(changePercent).toFixed(1)}%</span>
            </div>
          </div>
        </div>
      }
    >
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={data.data}>
            <defs>
              <linearGradient id="activeEmployeesGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#3B82F6" stopOpacity={0} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis dataKey="date" tickFormatter={formatXAxisLabel} stroke="#6B7280" fontSize={12} />
            <YAxis stroke="#6B7280" fontSize={12} />
            <Tooltip
              formatter={formatTooltipValue}
              labelStyle={{ color: '#374151' }}
              contentStyle={{
                backgroundColor: '#F9FAFB',
                border: '1px solid #E5E7EB',
                borderRadius: '6px',
              }}
            />
            <Area
              type="monotone"
              dataKey="value"
              stroke="#3B82F6"
              strokeWidth={2}
              fill="url(#activeEmployeesGradient)"
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      <div className="mt-4 text-xs text-gray-500 text-center">
        Biểu đồ số lượng nhân viên hoạt động theo thời gian
      </div>
    </Card>
  );
};

export default EmployeeActiveChart;
