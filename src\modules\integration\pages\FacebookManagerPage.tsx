import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  <PERSON>ton,
  Card,
  EmptyState,
  Icon,
  IconCard,
  Loading,
  Modal,
  Pagination,
  ResponsiveGrid,
  Typography,
} from '@/shared/components/common';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import {
  useDeleteFacebookPage,
  useGetFacebookPages,
  useToggleFacebookPageActive,
} from '../facebook/hooks/useFacebook';
import { FacebookPageDto, FacebookPageQueryDto } from '../facebook/types/facebook.types';
import PageWrapper from '@/shared/components/common/PageWrapper';
import { useNavigate } from 'react-router-dom';

// Facebook Page Card Component
interface FacebookPageCardProps {
  page: FacebookPageDto;
  onDelete: (pageId: string) => void;
  onToggleActive?: (pageId: string) => Promise<void> | void;
}

const FacebookPageCard: React.FC<FacebookPageCardProps> = ({ page, onDelete, onToggleActive }) => {
  const { t } = useTranslation('integration');
  const { success, error } = useSmartNotification();

  // Local state để đảm bảo UI cập nhật ngay lập tức (optimistic update)
  const [localActive, setLocalActive] = React.useState(page.isActive);
  const [isToggling, setIsToggling] = React.useState(false);

  // Sync local state với props khi props thay đổi
  React.useEffect(() => {
    setLocalActive(page.isActive);
  }, [page.isActive]);

  // Sử dụng local state để hiển thị
  const isActive = localActive;

  const handleToggleActive = async () => {
    if (!onToggleActive) return;

    const previousState = isActive;
    const newState = !isActive;

    try {
      // Cập nhật UI ngay lập tức (optimistic update)
      setLocalActive(newState);
      setIsToggling(true);

      // Gọi API
      await onToggleActive(page.facebookPageId);

      // Hiển thị thông báo thành công
      success({
        title: t('integration.facebook.toggleSuccess', 'Thành công'),
        message: newState
          ? t(
              'integration.facebook.activateSuccess',
              `Facebook Page "${page.pageName}" đã được kích hoạt`
            )
          : t(
              'integration.facebook.deactivateSuccess',
              `Facebook Page "${page.pageName}" đã được vô hiệu hóa`
            ),
        duration: 3000,
      });
    } catch (err) {
      console.error('Error toggling page active:', err);
      // Rollback local state nếu API thất bại
      setLocalActive(previousState);

      // Hiển thị thông báo lỗi
      error({
        title: t('integration.facebook.toggleError', 'Lỗi'),
        message:
          err instanceof Error
            ? err.message
            : t(
                'integration.facebook.toggleErrorMessage',
                'Có lỗi xảy ra khi thay đổi trạng thái Facebook Page'
              ),
        duration: 5000,
      });
    } finally {
      setIsToggling(false);
    }
  };

  return (
    <Card className="p-4 h-full flex flex-col">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full overflow-hidden">
              <img
                src={page.avatarPage || '/assets/images/default-avatar.png'}
                alt={page.pageName}
                className="w-full h-full object-cover border-radius-full"
              ></img>
            </div>
            {/* Status indicator */}
            <div
              className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                page.isError ? 'bg-red-500' : isActive ? 'bg-green-500' : 'bg-gray-400'
              }`}
            />
          </div>
          <div className="flex-1 min-w-0">
            <Typography variant="h6" className="font-semibold truncate">
              {page.pageName}
            </Typography>
            <Typography variant="body2" color="muted" className="text-sm">
              {page.facebookPersonalName}
            </Typography>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-end space-x-2">
        {/* Nút bật/tắt trạng thái */}
        {onToggleActive && (
          <div onClick={e => e.stopPropagation()}>
            <IconCard
              icon="power"
              variant={isActive ? 'primary' : 'default'}
              size="sm"
              onClick={handleToggleActive}
              className={isActive ? 'text-green-500' : 'text-gray-400'}
              disabled={isToggling}
              title={
                isActive
                  ? t('integration.facebook.deactivate', 'Vô hiệu hóa')
                  : t('integration.facebook.activate', 'Kích hoạt')
              }
              tooltipPosition="top"
            />
          </div>
        )}

        {/* Nút xóa */}
        <div className="flex-shrink-0">
          <IconCard
            icon="trash"
            title={t('common.delete', 'Xóa')}
            onClick={() => onDelete(page.facebookPageId)}
            className="text-gray-400 hover:text-red-500"
          />
        </div>
      </div>
    </Card>
  );
};

/**
 * Trang quản lý danh sách Facebook Pages đã liên kết
 */
const FacebookManagerPage: React.FC = () => {
  const { t } = useTranslation('integration');
  const { success, error: showError } = useSmartNotification();
  const navigate = useNavigate();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [pageToDelete, setPageToDelete] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(4);
  const [searchKeyword, setSearchKeyword] = useState('');

  // Query parameters
  const queryParams = useMemo(
    (): FacebookPageQueryDto => ({
      page: currentPage,
      limit: itemsPerPage,
      ...(searchKeyword && { keyword: searchKeyword }),
    }),
    [currentPage, itemsPerPage, searchKeyword]
  );

  // API hooks
  const { data: pagesData, isLoading, error, refetch } = useGetFacebookPages(queryParams);
  const deletePageMutation = useDeleteFacebookPage();
  const toggleActiveMutation = useToggleFacebookPageActive();

  const pages = pagesData?.result?.items || [];
  const totalItems = pagesData?.result?.meta?.totalItems || 0;

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Handle delete page confirmation
  const handleDeleteConfirm = (pageId: string) => {
    setPageToDelete(pageId);
    setShowDeleteConfirm(true);
  };

  // Handle delete page
  const handleDeletePage = async () => {
    if (!pageToDelete) return;

    // Tìm page để lấy tên
    const pageToDeleteInfo = pages.find(p => p.facebookPageId === pageToDelete);
    const pageName = pageToDeleteInfo?.pageName || 'Facebook Page';

    try {
      await deletePageMutation.mutateAsync(pageToDelete);
      setShowDeleteConfirm(false);
      setPageToDelete(null);

      // Hiển thị thông báo thành công
      success({
        title: t('integration.facebook.deleteSuccess', 'Thành công'),
        message: t(
          'integration.facebook.deletePageSuccess',
          `Facebook Page "${pageName}" đã được xóa thành công`
        ),
        duration: 3000,
      });
    } catch (err) {
      console.error('Error removing page:', err);

      // Hiển thị thông báo lỗi
      showError({
        title: t('integration.facebook.deleteError', 'Lỗi'),
        message:
          err instanceof Error
            ? err.message
            : t('integration.facebook.deleteErrorMessage', 'Có lỗi xảy ra khi xóa Facebook Page'),
        duration: 5000,
      });
    }
  };

  // Cancel delete
  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
    setPageToDelete(null);
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle retry
  const handleRetry = () => {
    refetch();
  };

  // Handle toggle active
  const handleToggleActive = async (pageId: string) => {
    try {
      await toggleActiveMutation.mutateAsync(pageId);
    } catch (error) {
      console.error('Error toggling page active:', error);
    }
  };

  // Handle add new Facebook page
  const handleAddFacebookPage = () => {
    navigate('/integrations/facebook');
  };

  return (
    <PageWrapper>
      {/* Menu Icon Bar với tìm kiếm và thêm mới */}
      {pages.length > 0 && <MenuIconBar onSearch={handleSearch} onAdd={handleAddFacebookPage} />}

      {isLoading ? (
        <div className="flex flex-col justify-center items-center py-12 sm:py-16">
          <Loading size="lg" />
        </div>
      ) : error ? (
        <EmptyState
          icon="alert-circle"
          title={t('common.error', 'Đã xảy ra lỗi')}
          description={t(
            'integration.facebook.loadError',
            'Không thể tải danh sách Facebook Pages. Vui lòng thử lại.'
          )}
          actions={
            <Button variant="outline" onClick={handleRetry}>
              <Icon name="refresh" size="sm" className="mr-2" />
              {t('common.retry', 'Thử lại')}
            </Button>
          }
        />
      ) : pages.length === 0 ? (
        <EmptyState
          icon="facebook"
          title={t('integration.facebook.noPages', 'Chưa có Facebook Page nào')}
          description={t(
            'integration.facebook.noPagesDescription',
            'Bạn chưa liên kết Facebook Page nào với hệ thống. Hãy thêm Facebook Page để bắt đầu sử dụng tính năng tích hợp.'
          )}
          actions={
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                variant="outline"
                onClick={() => navigate('/integrations')}
                className="flex items-center justify-center space-x-2"
              >
                <Icon name="arrow-left" size="sm" />
                <span>{t('common.back', 'Quay lại')}</span>
              </Button>
              <Button
                variant="primary"
                onClick={handleAddFacebookPage}
                className="flex items-center justify-center space-x-2"
              >
                <Icon name="plus" size="sm" />
                <span>{t('integration.facebook.addPage', 'Thêm Facebook Page')}</span>
              </Button>
            </div>
          }
        />
      ) : (
        <div className="space-y-4 sm:space-y-6">
          <ResponsiveGrid
            maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
            className="gap-4 sm:gap-6"
          >
            {pages.map(page => (
              <div key={page.facebookPageId} className="h-full">
                <FacebookPageCard
                  page={page}
                  onDelete={handleDeleteConfirm}
                  onToggleActive={handleToggleActive}
                />
              </div>
            ))}
          </ResponsiveGrid>
        </div>
      )}

      {/* Pagination - chỉ hiển thị khi có dữ liệu */}
      {!isLoading && !error && pages.length > 0 && (
        <div className="mt-6 flex justify-end">
          <Pagination
            variant="compact"
            borderless={true}
            currentPage={currentPage}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      )}

      {/* Confirmation Modal */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('common.confirm', 'Xác nhận xóa')}
        footer={
          <div className="flex flex-row justify-end gap-2 sm:gap-3">
            <IconCard
              icon="x"
              title={t('common.cancel', 'Hủy')}
              onClick={handleCancelDelete}
              variant="secondary"
              className="w-full sm:w-auto p-2"
            />
            <IconCard
              icon="trash"
              title={t('common.delete', 'Xóa')}
              onClick={handleDeletePage}
              variant="danger"
              className="w-full sm:w-auto p-2"
            />
          </div>
        }
      >
        <div className="p-4 sm:p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <Icon name="alert-circle" size="md" className="text-red-500" />
            </div>
            <div className="flex-1">
              <Typography variant="body1" className="mb-2">
                {t(
                  'integration.facebook.confirmDelete',
                  'Bạn có chắc chắn muốn xóa Facebook Page này khỏi hệ thống?'
                )}
              </Typography>
              <Typography variant="body2" color="muted" className="text-sm">
                {t(
                  'integration.facebook.confirmDeleteDescription',
                  'Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan sẽ bị xóa vĩnh viễn.'
                )}
              </Typography>
            </div>
          </div>
        </div>
      </Modal>
    </PageWrapper>
  );
};

export default FacebookManagerPage;
