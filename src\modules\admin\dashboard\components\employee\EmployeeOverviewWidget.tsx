import React, { useState, useEffect } from 'react';
import { Card, Skeleton } from '@/shared/components/common';
import { Users, UserCheck, TrendingUp, TrendingDown } from 'lucide-react';
import { cn } from '@/shared/utils/cn';
import {
  EmployeeApiService,
  type EmployeeOverviewApiData,
} from '../../services/api-integration.service';

// Sử dụng type từ API service

interface EmployeeOverviewWidgetProps {
  className?: string;
}

const EmployeeOverviewWidget: React.FC<EmployeeOverviewWidgetProps> = ({ className }) => {
  const [data, setData] = useState<EmployeeOverviewApiData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('🔄 Fetching employee overview data...');

        const result = await EmployeeApiService.getOverview();
        console.log('✅ Employee API Response data:', result);

        setData(result);
      } catch (err) {
        console.error('❌ Error fetching employee overview:', err);
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  const formatPercentage = (num: number): string => {
    return `${num.toFixed(1)}%`;
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const getGrowthIcon = (rate: number) => {
    return rate >= 0 ? (
      <TrendingUp className="h-4 w-4 text-green-500" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-500" />
    );
  };

  const getGrowthColor = (rate: number) => {
    return rate >= 0 ? 'text-green-600' : 'text-red-600';
  };

  if (loading) {
    return (
      <Card
        className={cn('h-full', className)}
        title={
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Tổng quan nhân viên
          </div>
        }
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton height="16px" width="80px" />
                <Skeleton height="32px" width="64px" />
              </div>
            ))}
          </div>
          <div className="space-y-2">
            <Skeleton height="16px" width="128px" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton height="24px" width="100%" />
              <Skeleton height="24px" width="100%" />
            </div>
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card
        className={cn('h-full', className)}
        title={
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Tổng quan nhân viên
          </div>
        }
      >
        <div className="flex items-center justify-center h-32 text-red-500">
          <p>Lỗi: {error}</p>
        </div>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card
        className={cn('h-full', className)}
        title={
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Tổng quan nhân viên
          </div>
        }
      >
        <div className="flex items-center justify-center h-32 text-gray-500">
          <p>Không có dữ liệu</p>
        </div>
      </Card>
    );
  }

  return (
    <Card
      className={cn('h-full', className)}
      title={
        <div>
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Tổng quan nhân viên
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Từ {new Date(data.dateRange.from).toLocaleDateString('vi-VN')} đến{' '}
            {new Date(data.dateRange.to).toLocaleDateString('vi-VN')}
          </p>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Overview Stats */}
        <div>
          <h4 className="text-sm font-medium mb-3">Thống kê tổng quan</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground">Tổng nhân viên</p>
              <p className="text-2xl font-bold text-blue-600">
                {formatNumber(data.overview.totalEmployees)}
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground">Nhân viên mới</p>
              <p className="text-2xl font-bold text-green-600">
                {formatNumber(data.overview.newEmployees)}
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground">Đang hoạt động</p>
              <p className="text-2xl font-bold text-emerald-600">
                {formatNumber(data.overview.activeEmployees)}
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground">Không hoạt động</p>
              <p className="text-2xl font-bold text-red-600">
                {formatNumber(data.overview.inactiveEmployees)}
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground">Đã xác thực</p>
              <p className="text-2xl font-bold text-purple-600">
                {formatNumber(data.overview.verifiedEmployees)}
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground">Chưa xác thực</p>
              <p className="text-2xl font-bold text-orange-600">
                {formatNumber(data.overview.unverifiedEmployees)}
              </p>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div>
          <h4 className="text-sm font-medium mb-3">Hiệu suất</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-xs text-muted-foreground">Tăng trưởng nhân viên</p>
                <p
                  className={cn(
                    'text-sm font-medium',
                    getGrowthColor(data.performance.employeeGrowthRate)
                  )}
                >
                  {formatPercentage(data.performance.employeeGrowthRate)}
                </p>
              </div>
              {getGrowthIcon(data.performance.employeeGrowthRate)}
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-xs text-muted-foreground">Tỷ lệ giữ chân</p>
                <p className="text-sm font-medium text-green-600">
                  {formatPercentage(data.performance.retentionRate)}
                </p>
              </div>
              <UserCheck className="h-4 w-4 text-green-500" />
            </div>
          </div>
        </div>

        {/* Activity Summary */}
        <div>
          <h4 className="text-sm font-medium mb-3">Hoạt động</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Tổng đăng nhập:</span>
              <span className="font-medium">{formatNumber(data.activity.totalLogins)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Đăng nhập duy nhất:</span>
              <span className="font-medium">{formatNumber(data.activity.uniqueLogins)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Thời gian TB:</span>
              <span className="font-medium">
                {formatDuration(data.activity.averageSessionDuration)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Điểm hiệu suất TB:</span>
              <span className="font-medium">{data.performance.averagePerformanceScore}/10</span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default EmployeeOverviewWidget;
